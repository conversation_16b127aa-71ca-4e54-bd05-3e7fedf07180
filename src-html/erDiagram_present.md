erDiagram
    polling_stations {
        int id PK
        string name UK
        datetime created_at
    }

    sections {
        int id PK
        string name
        int polling_station_id FK
        datetime created_at
    }

    voters {
        int id PK
        string name
        string relationship_type
        string relationship_name
        string gender
        int birth_year
        string epic_number UK
        string house_number
        int polling_station_id FK
        int section_id FK
        string phone
        string email
        string facebook
        string instagram
        string twitter
        string status
        string supporter_status
        string education
        string occupation
        string community
        string religion
        string economic_status
        string custom_notes
        datetime created_at
        datetime updated_at
    }

    transactions {
        int id PK
        int voter_id FK
        date date
        string purpose
        decimal amount
        datetime created_at
    }

    settings {
        int id PK
        string category
        string value
        datetime created_at
    }

    polling_stations ||--o{ sections : "has many"
    polling_stations ||--o{ voters : "has many"
    sections ||--o{ voters : "has many"
    voters ||--o{ transactions : "has many"
