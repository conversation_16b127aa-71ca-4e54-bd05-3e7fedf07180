erDiagram
    polling_stations {
        int id PK
        string name UK
        datetime created_at
    }

    sections {
        int id PK
        string name
        int polling_station_id FK
        datetime created_at
    }

    genders {
        tinyint id PK
        string code UK
    }

    religions {
        tinyint id PK
        string name UK
    }

    communities {
        smallint id PK
        string name UK
    }

    education_levels {
        smallint id PK
        string name UK
    }

    occupations {
        smallint id PK
        string name UK
    }

    economic_statuses {
        tinyint id PK
        string name UK
    }

    supporter_statuses {
        tinyint id PK
        string name UK
    }

    voters {
        bigint id PK
        string name
        string epic_number UK
        string house_number
        year birth_year
        tinyint gender_id FK
        tinyint religion_id FK
        smallint community_id FK
        smallint education_id FK
        smallint occupation_id FK
        tinyint economic_status_id FK
        tinyint supporter_status_id FK
        int polling_station_id FK
        int section_id FK
        datetime created_at
        datetime updated_at
    }

    voter_contacts {
        int id PK
        bigint voter_id FK
        string type
        string value
        datetime created_at
    }

    voter_notes {
        int id PK
        bigint voter_id FK
        text note
        datetime created_at
    }

    transactions {
        int id PK
        bigint voter_id FK
        date date
        string purpose
        decimal(10) amount
        datetime created_at
    }

    settings {
        int id PK
        string category
        string value
        datetime created_at
    }

    %% Relationships
    polling_stations ||--o{ sections : "has many"
    polling_stations ||--o{ voters : "has many"
    sections ||--o{ voters : "has many"
    voters ||--o{ transactions : "has many"
    voters ||--o{ voter_contacts : "has many"
    voters ||--o{ voter_notes : "has many"

    genders           ||--o{ voters : "has many"
    religions         ||--o{ voters : "has many"
    communities       ||--o{ voters : "has many"
    education_levels  ||--o{ voters : "has many"
    occupations       ||--o{ voters : "has many"
    economic_statuses ||--o{ voters : "has many"
    supporter_statuses||--o{ voters : "has many"