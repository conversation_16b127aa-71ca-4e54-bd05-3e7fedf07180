/**
 * Animation utilities for the Voter Management Dashboard
 */
import { CONFIG } from './config.js';
import { forceReflow } from './utils.js';

/**
 * Animation manager class
 */
export class AnimationManager {
  constructor() {
    this.activeAnimations = new Set();
  }

  /**
   * Triggers a light sweep animation on a container
   * @param {HTMLElement} container - Element to animate
   * @returns {Promise} - Resolves when animation completes
   */
  triggerLightSweep(container) {
    if (!container) return Promise.resolve();

    return new Promise((resolve) => {
      // Clean up any existing animation
      container.classList.remove('animate-sweep');
      forceReflow(container);

      // Start new animation
      setTimeout(() => {
        container.classList.add('animate-sweep');
        this.activeAnimations.add(container);

        // Clean up after animation
        setTimeout(() => {
          container.classList.remove('animate-sweep');
          this.activeAnimations.delete(container);
          resolve();
        }, CONFIG.ANIMATION.SWEEP_CLEANUP);
      }, CONFIG.ANIMATION.SWEEP_DELAY);
    });
  }

  /**
   * Animates panel opening/closing
   * @param {HTMLElement} panel - Panel element
   * @param {boolean} isOpening - Whether panel is opening
   * @returns {Promise} - Resolves when animation completes
   */
  animatePanel(panel, isOpening) {
    if (!panel) return Promise.resolve();

    return new Promise((resolve) => {
      if (isOpening) {
        panel.classList.add('is-open');
      } else {
        panel.classList.remove('is-open');
      }

      // Wait for CSS transition to complete
      setTimeout(resolve, 250); // Match CSS transition duration
    });
  }

  /**
   * Animates dropdown opening/closing
   * @param {HTMLElement} dropdown - Dropdown element
   * @param {boolean} isOpening - Whether dropdown is opening
   * @returns {Promise} - Resolves when animation completes
   */
  animateDropdown(dropdown, isOpening) {
    if (!dropdown) return Promise.resolve();

    return new Promise((resolve) => {
      if (isOpening) {
        dropdown.classList.add('is-open');
      } else {
        dropdown.classList.remove('is-open');
      }

      // Wait for CSS transition to complete
      setTimeout(resolve, 200); // Match CSS transition duration
    });
  }

  /**
   * Cleans up all active animations
   */
  cleanup() {
    this.activeAnimations.forEach(container => {
      container.classList.remove('animate-sweep');
    });
    this.activeAnimations.clear();
  }
}

// Export singleton instance
export const animationManager = new AnimationManager();