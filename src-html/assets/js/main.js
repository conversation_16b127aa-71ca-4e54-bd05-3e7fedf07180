/**
 * Main entry point for the Voter Management Dashboard
 * Tauri Desktop Application
 */
import { CONFIG } from './config.js';
import { themeManager } from './theme-manager.js';
import { dataManager } from './data-manager.js';
import { uiManager } from './ui-manager.js';
import { animationManager } from './animations.js';

/**
 * Main application class
 */
class VoterManagementApp {
  constructor() {
    this.isInitialized = false;
    this.managers = {
      theme: themeManager,
      data: dataManager,
      ui: uiManager,
      animation: animationManager
    };
  }

  /**
   * Initialize the application
   */
  async init() {
    if (this.isInitialized) {
      console.warn('Application already initialized');
      return;
    }

    try {
      console.log('Initializing Voter Management Dashboard...');

      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // Initialize managers (they're already initialized as singletons)
      // but we can add any additional setup here

      // Set up error handling
      this.setupErrorHandling();

      // Set up performance monitoring
      this.setupPerformanceMonitoring();

      // Mark as initialized
      this.isInitialized = true;

      console.log('Voter Management Dashboard initialized successfully');

      // Dispatch custom event for any external listeners
      document.dispatchEvent(new CustomEvent('app:initialized', {
        detail: { app: this }
      }));

    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.handleInitializationError(error);
    }
  }

  /**
   * Set up global error handling
   */
  setupErrorHandling() {
    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      console.error('Uncaught error:', event.error);
      this.handleError(event.error, 'Uncaught Error');
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.handleError(event.reason, 'Unhandled Promise Rejection');
    });
  }

  /**
   * Set up performance monitoring
   */
  setupPerformanceMonitoring() {
    // Monitor long tasks (if supported)
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.duration > 50) { // Tasks longer than 50ms
              console.warn(`Long task detected: ${entry.duration}ms`);
            }
          });
        });
        observer.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        // PerformanceObserver might not support longtask
        console.log('Long task monitoring not available');
      }
    }

    // Log initial performance metrics
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0];
        if (perfData) {
          console.log('Performance metrics:', {
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
            totalTime: perfData.loadEventEnd - perfData.fetchStart
          });
        }
      }, 0);
    });
  }

  /**
   * Handle application errors
   * @param {Error} error - Error object
   * @param {string} context - Error context
   */
  handleError(error, context = 'Application Error') {
    // In a Tauri app, you might want to send errors to the backend
    // or display them in a user-friendly way

    const errorInfo = {
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace available',
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Log to console for development
    console.error(`${context}:`, errorInfo);

    // In production, you might want to:
    // 1. Send to error reporting service
    // 2. Show user-friendly error message
    // 3. Attempt recovery if possible
  }

  /**
   * Handle initialization errors
   * @param {Error} error - Initialization error
   */
  handleInitializationError(error) {
    // Show a fallback UI or error message
    const errorMessage = document.createElement('div');
    errorMessage.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #ff3b30;
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        z-index: 9999;
      ">
        <h3>Application Failed to Initialize</h3>
        <p>Please refresh the page or contact support.</p>
        <button onclick="window.location.reload()" style="
          background: white;
          color: #ff3b30;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 10px;
        ">Reload</button>
      </div>
    `;
    document.body.appendChild(errorMessage);
  }

  /**
   * Get manager instance
   * @param {string} name - Manager name
   * @returns {Object|null} - Manager instance
   */
  getManager(name) {
    return this.managers[name] || null;
  }

  /**
   * Check if application is initialized
   * @returns {boolean} - Initialization status
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * Cleanup application resources
   */
  cleanup() {
    console.log('Cleaning up application resources...');

    // Cleanup managers
    if (this.managers.ui && typeof this.managers.ui.cleanup === 'function') {
      this.managers.ui.cleanup();
    }

    if (this.managers.animation && typeof this.managers.animation.cleanup === 'function') {
      this.managers.animation.cleanup();
    }

    this.isInitialized = false;
  }
}

// Create and initialize the application
const app = new VoterManagementApp();

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// Handle page unload
window.addEventListener('beforeunload', () => {
  app.cleanup();
});

// Export for global access (useful for debugging in Tauri)
window.VoterApp = app;

// Export for module usage
export default app;