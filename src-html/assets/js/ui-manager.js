/**
 * UI management for the Voter Management Dashboard
 */
import { CONFIG } from './config.js';
import { safeQuery, safeQueryAll, debounce } from './utils.js';
import { animationManager } from './animations.js';
import { dataManager } from './data-manager.js';

/**
 * UI manager class for handling user interface interactions
 */
export class UIManager {
  constructor() {
    this.elements = {};
    this.activeDropdown = null;
    this.debouncedSearch = debounce(this.handleSearch.bind(this), CONFIG.DEBOUNCE.SEARCH);

    this.init();
  }

  /**
   * Initialize UI manager
   */
  init() {
    this.cacheElements();
    this.bindEvents();
    this.renderInitialData();
  }

  /**
   * Cache DOM elements for better performance
   */
  cacheElements() {
    // Main UI elements
    this.elements.filterBtn = safeQuery(CONFIG.SELECTORS.FILTER_BTN);
    this.elements.filterContainer = safeQuery(CONFIG.SELECTORS.FILTER_CONTAINER);
    this.elements.clearFiltersBtn = safeQuery(CONFIG.SELECTORS.CLEAR_FILTERS);
    this.elements.voterPanel = safeQuery(CONFIG.SELECTORS.VOTER_PANEL);
    this.elements.dataTableBody = safeQuery(CONFIG.SELECTORS.DATA_TABLE_BODY);
    this.elements.panelCloseButtons = safeQueryAll(CONFIG.SELECTORS.PANEL_CLOSE);

    // Dropdowns
    this.elements.columnsBtn = safeQuery(`#${CONFIG.SELECTORS.COLUMNS_BTN}`);
    this.elements.columnsDropdown = safeQuery(`#${CONFIG.SELECTORS.COLUMNS_DROPDOWN}`);
    this.elements.settingsBtn = safeQuery(`#${CONFIG.SELECTORS.SETTINGS_BTN}`);
    this.elements.settingsDropdown = safeQuery(`#${CONFIG.SELECTORS.SETTINGS_DROPDOWN}`);

    // Settings Dashboard
    this.elements.manageDataBtn = safeQuery(`#${CONFIG.SELECTORS.MANAGE_DATA_BTN}`);
    this.elements.settingsDashboardContainer = safeQuery(CONFIG.SELECTORS.SETTINGS_DASHBOARD_CONTAINER);
    this.elements.categorySelect = safeQuery(`#${CONFIG.SELECTORS.CATEGORY_SELECT}`);
    this.elements.newItemInput = safeQuery(`#${CONFIG.SELECTORS.NEW_ITEM_INPUT}`);
    this.elements.addItemBtn = safeQuery(`#${CONFIG.SELECTORS.ADD_ITEM_BTN}`);
    this.elements.itemList = safeQuery(`#${CONFIG.SELECTORS.ITEM_LIST}`);
    this.elements.dataListTitle = safeQuery(CONFIG.SELECTORS.DATA_LIST_TITLE);

    // Reports
    this.elements.reportsBtn = safeQuery(`#${CONFIG.SELECTORS.REPORTS_BTN}`);
    this.elements.mainView = safeQuery(`#${CONFIG.SELECTORS.MAIN_VIEW}`);
    this.elements.reportsView = safeQuery(`#${CONFIG.SELECTORS.REPORTS_VIEW}`);
    this.elements.backToMainBtn = safeQuery(`#${CONFIG.SELECTORS.BACK_TO_MAIN}`);

    // Search
    this.elements.searchInput = safeQuery('.search-input');
  }

  /**
   * Bind event listeners
   */
  bindEvents() {
    // Filter panel toggle
    if (this.elements.filterBtn) {
      this.elements.filterBtn.addEventListener('click', this.handleFilterToggle.bind(this));
    }

    // Clear filters
    if (this.elements.clearFiltersBtn) {
      this.elements.clearFiltersBtn.addEventListener('click', this.handleClearFilters.bind(this));
    }

    // Voter detail panel
    if (this.elements.dataTableBody) {
      this.elements.dataTableBody.addEventListener('click', this.handleTableRowClick.bind(this));
    }

    // Panel close buttons
    this.elements.panelCloseButtons.forEach(button => {
      button.addEventListener('click', this.handlePanelClose.bind(this));
    });

    // Dropdowns
    if (this.elements.columnsBtn) {
      this.elements.columnsBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleDropdown(this.elements.columnsDropdown);
      });
    }

    if (this.elements.settingsBtn) {
      this.elements.settingsBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleDropdown(this.elements.settingsDropdown);
      });
    }

    // Settings dashboard
    if (this.elements.manageDataBtn) {
      this.elements.manageDataBtn.addEventListener('click', this.handleManageDataToggle.bind(this));
    }

    if (this.elements.categorySelect) {
      this.elements.categorySelect.addEventListener('change', this.handleCategoryChange.bind(this));
    }

    if (this.elements.addItemBtn) {
      this.elements.addItemBtn.addEventListener('click', this.handleAddItem.bind(this));
    }

    if (this.elements.newItemInput) {
      this.elements.newItemInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.handleAddItem();
        }
      });
    }

    if (this.elements.itemList) {
      this.elements.itemList.addEventListener('click', this.handleItemListClick.bind(this));
    }

    // Reports
    if (this.elements.reportsBtn) {
      this.elements.reportsBtn.addEventListener('click', this.handleReportsView.bind(this));
    }

    if (this.elements.backToMainBtn) {
      this.elements.backToMainBtn.addEventListener('click', this.handleMainView.bind(this));
    }

    // Search
    if (this.elements.searchInput) {
      this.elements.searchInput.addEventListener('input', (e) => {
        this.debouncedSearch(e.target.value);
      });
    }

    // Global click handler for closing dropdowns
    document.addEventListener('click', this.handleGlobalClick.bind(this));

    // Column dropdown items
    const columnItems = safeQueryAll('#columns-dropdown .dropdown-item');
    columnItems.forEach(item => {
      item.addEventListener('click', this.handleColumnToggle.bind(this));
    });
  }

  /**
   * Handle filter panel toggle
   */
  handleFilterToggle() {
    const isOpening = !this.elements.filterContainer.classList.contains('is-open');
    this.elements.filterContainer.classList.toggle('is-open');
    this.elements.filterBtn.classList.toggle('is-active', isOpening);

    if (isOpening) {
      animationManager.triggerLightSweep(this.elements.filterContainer);
    }
  }

  /**
   * Handle clear filters button
   */
  handleClearFilters() {
    dataManager.clearFilters();
    this.resetFilterInputs();
    animationManager.triggerLightSweep(this.elements.filterContainer);
  }

  /**
   * Handle table row click to open voter detail panel
   * @param {Event} e - Click event
   */
  handleTableRowClick(e) {
    const row = e.target.closest('tr');
    if (row) {
      animationManager.animatePanel(this.elements.voterPanel, true);
    }
  }

  /**
   * Handle panel close button click
   * @param {Event} e - Click event
   */
  handlePanelClose(e) {
    const panel = e.target.closest('.voter-detail-panel, .settings-dashboard-container');
    if (panel) {
      animationManager.animatePanel(panel, false);
    }
  }

  /**
   * Toggle dropdown visibility
   * @param {HTMLElement} dropdown - Dropdown element
   */
  toggleDropdown(dropdown) {
    if (!dropdown) return;

    // Close other dropdowns
    if (this.activeDropdown && this.activeDropdown !== dropdown) {
      this.activeDropdown.classList.remove('is-open');
    }

    const isOpening = !dropdown.classList.contains('is-open');
    dropdown.classList.toggle('is-open');

    this.activeDropdown = isOpening ? dropdown : null;
  }

  /**
   * Handle global click to close dropdowns
   * @param {Event} e - Click event
   */
  handleGlobalClick(e) {
    if (this.activeDropdown && !e.target.closest('.dropdown-container')) {
      this.activeDropdown.classList.remove('is-open');
      this.activeDropdown = null;
    }
  }

  /**
   * Handle manage data toggle
   */
  handleManageDataToggle() {
    const isOpening = !this.elements.settingsDashboardContainer.classList.contains('is-open');
    this.elements.settingsDashboardContainer.classList.toggle('is-open');

    if (isOpening) {
      animationManager.triggerLightSweep(this.elements.settingsDashboardContainer);
    }

    // Close settings dropdown
    if (this.elements.settingsDropdown) {
      this.elements.settingsDropdown.classList.remove('is-open');
    }
  }

  /**
   * Handle category change in settings
   */
  handleCategoryChange() {
    const category = this.elements.categorySelect.value;
    this.renderCategoryList(category);
  }

  /**
   * Handle add item button click
   */
  handleAddItem() {
    const category = this.elements.categorySelect.value;
    const newItem = this.elements.newItemInput.value.trim();

    if (newItem && dataManager.addCategoryItem(category, newItem)) {
      this.renderCategoryList(category);
      this.elements.newItemInput.value = '';
    }
  }

  /**
   * Handle item list clicks (edit/delete)
   * @param {Event} e - Click event
   */
  handleItemListClick(e) {
    const category = this.elements.categorySelect.value;

    if (e.target.classList.contains('delete-btn')) {
      const itemText = e.target.parentElement.previousElementSibling.textContent;
      if (dataManager.removeCategoryItem(category, itemText)) {
        this.renderCategoryList(category);
      }
    }

    // Edit functionality can be added here if needed
  }

  /**
   * Handle reports view
   * @param {Event} e - Click event
   */
  handleReportsView(e) {
    e.preventDefault();
    this.elements.mainView.style.display = 'none';
    this.elements.reportsView.style.display = 'block';
  }

  /**
   * Handle main view
   * @param {Event} e - Click event
   */
  handleMainView(e) {
    e.preventDefault();
    this.elements.reportsView.style.display = 'none';
    this.elements.mainView.style.display = 'block';
  }

  /**
   * Handle column toggle in dropdown
   * @param {Event} e - Click event
   */
  handleColumnToggle(e) {
    const item = e.currentTarget;
    item.classList.toggle('is-selected');
    // Column visibility logic would go here
  }

  /**
   * Handle search input
   * @param {string} searchTerm - Search term
   */
  handleSearch(searchTerm) {
    dataManager.updateFilter('searchTerm', searchTerm);
    // Update table display would go here
  }

  /**
   * Render category list in settings panel
   * @param {string} category - Category to render
   */
  renderCategoryList(category) {
    if (!this.elements.itemList || !this.elements.dataListTitle) return;

    this.elements.itemList.innerHTML = '';
    const items = dataManager.getCategoryData(category);

    items.forEach(item => {
      const li = document.createElement('li');
      li.innerHTML = `
        <span>${item}</span>
        <div class="item-actions">
          <button class="edit-btn">Edit</button>
          <button class="delete-btn">Delete</button>
        </div>
      `;
      this.elements.itemList.appendChild(li);
    });

    this.elements.dataListTitle.textContent = dataManager.getFormattedCategoryName(category);
  }

  /**
   * Reset filter inputs to default values
   */
  resetFilterInputs() {
    const filters = dataManager.getFilters();

    // Reset select elements
    const genderSelect = safeQuery('#gender-select');
    const communitySelect = safeQuery('#community-select');
    const religionSelect = safeQuery('#religion-select');
    const economicSelect = safeQuery('#economic-select');

    if (genderSelect) genderSelect.value = filters.gender;
    if (communitySelect) communitySelect.value = filters.community;
    if (religionSelect) religionSelect.value = filters.religion;
    if (economicSelect) economicSelect.value = filters.economicStatus;

    // Reset number inputs
    const ageFromInput = safeQuery('#age-from');
    const ageToInput = safeQuery('#age-to');

    if (ageFromInput) ageFromInput.value = filters.ageFrom;
    if (ageToInput) ageToInput.value = filters.ageTo;

    // Reset search
    if (this.elements.searchInput) {
      this.elements.searchInput.value = filters.searchTerm;
    }
  }

  /**
   * Render initial data
   */
  renderInitialData() {
    this.renderCategoryList(CONFIG.DATA_CATEGORIES.COMMUNITY);
  }

  /**
   * Get cached element
   * @param {string} key - Element key
   * @returns {HTMLElement|null} - Cached element
   */
  getElement(key) {
    return this.elements[key] || null;
  }

  /**
   * Cleanup method for removing event listeners
   */
  cleanup() {
    // Remove global event listeners
    document.removeEventListener('click', this.handleGlobalClick.bind(this));

    // Clear active dropdown reference
    this.activeDropdown = null;
  }
}

// Export singleton instance
export const uiManager = new UIManager();