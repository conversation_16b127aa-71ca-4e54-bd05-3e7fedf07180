/**
 * Theme management for the Voter Management Dashboard
 */
import { CONFIG } from './config.js';
import { getStorageItem, setStorageItem, safeQuery } from './utils.js';

/**
 * Theme manager class
 */
export class ThemeManager {
  constructor() {
    this.html = document.documentElement;
    this.themeToggle = null;
    this.currentTheme = this.getInitialTheme();

    this.init();
  }

  /**
   * Initialize theme manager
   */
  init() {
    this.themeToggle = safeQuery(`#${CONFIG.SELECTORS.THEME_TOGGLE}`);
    this.applyTheme(this.currentTheme);
    this.bindEvents();
    this.updateToggleButton();
  }

  /**
   * Get initial theme from storage or system preference
   * @returns {string} - Theme name
   */
  getInitialTheme() {
    const savedTheme = getStorageItem(CONFIG.STORAGE_KEYS.THEME);
    if (savedTheme && CONFIG.THEMES[savedTheme.toUpperCase()]) {
      return savedTheme;
    }

    // Fallback to system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? CONFIG.THEMES.DARK
      : CONFIG.THEMES.LIGHT;
  }

  /**
   * Apply theme to document
   * @param {string} theme - Theme name
   */
  applyTheme(theme) {
    if (!CONFIG.THEMES[theme.toUpperCase()]) {
      console.warn(`Invalid theme: ${theme}`);
      return;
    }

    this.html.setAttribute('data-theme', theme);
    this.currentTheme = theme;
    setStorageItem(CONFIG.STORAGE_KEYS.THEME, theme);
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const newTheme = this.currentTheme === CONFIG.THEMES.DARK
      ? CONFIG.THEMES.LIGHT
      : CONFIG.THEMES.DARK;

    this.applyTheme(newTheme);
    this.updateToggleButton();
  }

  /**
   * Update toggle button text and icon
   */
  updateToggleButton() {
    if (!this.themeToggle) return;

    const isLight = this.currentTheme === CONFIG.THEMES.LIGHT;
    const textSpan = this.themeToggle.querySelector('svg + *');

    if (textSpan) {
      textSpan.textContent = isLight ? 'Light' : 'Dark';
    }

    // Update aria-label for better accessibility in desktop app
    this.themeToggle.setAttribute('aria-label',
      `Switch to ${isLight ? 'dark' : 'light'} theme`);
  }

  /**
   * Bind theme-related events
   */
  bindEvents() {
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)')
      .addEventListener('change', (e) => {
        // Only auto-switch if user hasn't manually set a preference
        const savedTheme = getStorageItem(CONFIG.STORAGE_KEYS.THEME);
        if (!savedTheme) {
          const systemTheme = e.matches ? CONFIG.THEMES.DARK : CONFIG.THEMES.LIGHT;
          this.applyTheme(systemTheme);
          this.updateToggleButton();
        }
      });
  }

  /**
   * Get current theme
   * @returns {string} - Current theme name
   */
  getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * Check if current theme is dark
   * @returns {boolean} - True if dark theme is active
   */
  isDarkTheme() {
    return this.currentTheme === CONFIG.THEMES.DARK;
  }
}

// Export singleton instance
export const themeManager = new ThemeManager();