/**
 * Settings service wrapper for JavaScript
 * This bridges the TypeScript SettingsService with the JavaScript data manager
 */

/**
 * JavaScript wrapper for SettingsService
 */
export class SettingsServiceWrapper {
  constructor() {
    this.isInitialized = false;
    this.settingsService = null;
  }

  /**
   * Initialize with the actual TypeScript SettingsService
   * @param {Object} settingsService - The TypeScript SettingsService instance
   */
  async initialize(settingsService) {
    this.settingsService = settingsService;
    
    if (settingsService) {
      // Initialize default settings
      await settingsService.initializeDefaultSettings();
      this.isInitialized = true;
      console.log('✅ Settings service initialized');
    }
  }

  /**
   * Get all settings for a category
   * @param {string} category - Category name
   * @returns {Promise<string[]>} - Array of setting values
   */
  async getCategorySettings(category) {
    if (!this.settingsService) {
      console.warn('Settings service not initialized');
      return [];
    }

    try {
      return await this.settingsService.getCategorySettings(category);
    } catch (error) {
      console.error('Failed to get category settings:', error);
      return [];
    }
  }

  /**
   * Add a new setting to a category
   * @param {string} category - Category name
   * @param {string} value - Setting value
   * @returns {Promise<boolean>} - Success status
   */
  async addCategorySetting(category, value) {
    if (!this.settingsService) {
      console.warn('Settings service not initialized');
      return false;
    }

    try {
      return await this.settingsService.addCategorySetting(category, value);
    } catch (error) {
      console.error('Failed to add category setting:', error);
      return false;
    }
  }

  /**
   * Update a setting in a category
   * @param {string} category - Category name
   * @param {string} oldValue - Current value
   * @param {string} newValue - New value
   * @returns {Promise<boolean>} - Success status
   */
  async updateCategorySetting(category, oldValue, newValue) {
    if (!this.settingsService) {
      console.warn('Settings service not initialized');
      return false;
    }

    try {
      return await this.settingsService.updateCategorySetting(category, oldValue, newValue);
    } catch (error) {
      console.error('Failed to update category setting:', error);
      return false;
    }
  }

  /**
   * Remove a setting from a category
   * @param {string} category - Category name
   * @param {string} value - Setting value
   * @returns {Promise<boolean>} - Success status
   */
  async removeCategorySetting(category, value) {
    if (!this.settingsService) {
      console.warn('Settings service not initialized');
      return false;
    }

    try {
      return await this.settingsService.removeCategorySetting(category, value);
    } catch (error) {
      console.error('Failed to remove category setting:', error);
      return false;
    }
  }

  /**
   * Get all categories that have settings
   * @returns {Promise<string[]>} - Array of category names
   */
  async getAllCategories() {
    if (!this.settingsService) {
      console.warn('Settings service not initialized');
      return [];
    }

    try {
      return await this.settingsService.getAllCategories();
    } catch (error) {
      console.error('Failed to get all categories:', error);
      return [];
    }
  }
}

// Export singleton instance
export const settingsServiceWrapper = new SettingsServiceWrapper();
