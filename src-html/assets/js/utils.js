/**
 * Utility functions for the Voter Management Dashboard
 */

/**
 * Safely queries for a DOM element
 * @param {string} selector - CSS selector or element ID
 * @param {Document|Element} context - Context to search within
 * @returns {Element|null} - Found element or null
 */
export function safeQuery(selector, context = document) {
  try {
    // Use getElementById for better performance if selector starts with #
    if (typeof selector === 'string' && selector.startsWith('#')) {
      return document.getElementById(selector.slice(1));
    }
    return context.querySelector(selector);
  } catch (error) {
    console.warn(`Invalid selector: ${selector}`, error);
    return null;
  }
}

/**
 * Safely queries for multiple DOM elements
 * @param {string} selector - CSS selector
 * @param {Document|Element} context - Context to search within
 * @returns {NodeList} - Found elements or empty NodeList
 */
export function safeQueryAll(selector, context = document) {
  try {
    return context.querySelectorAll(selector);
  } catch (error) {
    console.warn(`Invalid selector: ${selector}`, error);
    return document.querySelectorAll(''); // Empty NodeList
  }
}

/**
 * Creates a debounced function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Creates a throttled function
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} - Throttled function
 */
export function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Capitalizes the first letter of a string and replaces underscores with spaces
 * @param {string} str - String to format
 * @returns {string} - Formatted string
 */
export function formatCategoryName(str) {
  return str.charAt(0).toUpperCase() + str.slice(1).replace('_', ' ');
}

/**
 * Safely gets a value from localStorage
 * @param {string} key - Storage key
 * @param {*} defaultValue - Default value if key doesn't exist
 * @returns {*} - Stored value or default
 */
export function getStorageItem(key, defaultValue = null) {
  try {
    const item = localStorage.getItem(key);
    return item !== null ? item : defaultValue;
  } catch (error) {
    console.warn(`Error reading from localStorage: ${key}`, error);
    return defaultValue;
  }
}

/**
 * Safely sets a value in localStorage
 * @param {string} key - Storage key
 * @param {*} value - Value to store
 * @returns {boolean} - Success status
 */
export function setStorageItem(key, value) {
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (error) {
    console.warn(`Error writing to localStorage: ${key}`, error);
    return false;
  }
}

/**
 * Forces a reflow to ensure CSS changes are applied
 * @param {Element} element - Element to force reflow on
 */
export function forceReflow(element) {
  void element.offsetWidth;
}