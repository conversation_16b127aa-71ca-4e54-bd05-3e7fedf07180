/**
 * Configuration constants for the Voter Management Dashboard
 */
export const CONFIG = {
  ANIMATION: {
    DURATION: 1500,
    SWEEP_DELAY: 10,
    SWEEP_CLEANUP: 1500
  },

  DEBOUNCE: {
    SEARCH: 300,
    FILTER: 200
  },

  THEMES: {
    LIGHT: 'light',
    DARK: 'dark'
  },

  STORAGE_KEYS: {
    THEME: 'theme'
  },

  SELECTORS: {
    // Main UI elements
    FILTER_BTN: '.filter-btn',
    FILTER_CONTAINER: '.filter-options-container',
    CLEAR_FILTERS: '.clear-filters',
    VOTER_PANEL: '.voter-detail-panel',
    DATA_TABLE_BODY: '.data-table tbody',
    PANEL_CLOSE: '.panel-close',

    // Dropdowns
    COLUMNS_BTN: 'columns-btn',
    COLUMNS_DROPDOWN: 'columns-dropdown',
    SETTINGS_BTN: 'settings-btn',
    SETTINGS_DROPDOWN: 'settings-dropdown',

    // Theme
    THEME_TOGGLE: 'themeToggle',

    // Settings Dashboard
    MANAGE_DATA_BTN: 'manage-data-btn',
    SETTINGS_DASHBOARD_CONTAINER: '.settings-dashboard-container',
    CATEGORY_SELECT: 'category-select',
    NEW_ITEM_INPUT: 'new-item-input',
    ADD_ITEM_BTN: 'add-item-btn',
    ITEM_LIST: 'item-list',
    DATA_LIST_TITLE: '.data-list h3',

    // Reports
    REPORTS_BTN: 'reports-btn',
    MAIN_VIEW: 'main-view',
    REPORTS_VIEW: 'reports-view',
    BACK_TO_MAIN: 'back-to-main'
  },

  DATA_CATEGORIES: {
    COMMUNITY: 'community',
    RELIGION: 'religion',
    ECONOMIC_STATUS: 'economic_status'
  }
};