/**
 * Data management for the Voter Management Dashboard
 */
import { CONFIG } from './config.js';
import { formatCategoryName } from './utils.js';

/**
 * Data manager class for handling voter and category data
 */
export class DataManager {
  constructor() {
    this.data = this.initializeData();
    this.filters = this.initializeFilters();
    this.voterData = this.initializeVoterData();
    this.settingsService = null;
    this.isInitialized = false;
  }

  /**
   * Initialize with database service
   * @param {Object} settingsService - Settings service instance
   */
  async initialize(settingsService) {
    this.settingsService = settingsService;
    if (settingsService) {
      await this.loadCategoriesFromDatabase();
      this.isInitialized = true;
    } else {
      // Load from localStorage if no database service
      this.loadCategoriesFromStorage();
    }
  }

  /**
   * Initialize category data
   * @returns {Object} - Category data object
   */
  initializeData() {
    // Try to load from localStorage first
    const savedData = this.loadCategoriesFromStorage();
    if (savedData) {
      return savedData;
    }

    // Default data if nothing in localStorage
    return {
      [CONFIG.DATA_CATEGORIES.COMMUNITY]: ['General', 'OBC', 'SC', 'ST'],
      [CONFIG.DATA_CATEGORIES.RELIGION]: ['Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain', 'Other'],
      [CONFIG.DATA_CATEGORIES.ECONOMIC_STATUS]: ['APL', 'BPL', 'Middle Income', 'High Income']
    };
  }

  /**
   * Load categories from localStorage
   * @returns {Object|null} - Category data or null if not found
   */
  loadCategoriesFromStorage() {
    try {
      const saved = localStorage.getItem('electixir_categories');
      if (saved) {
        const data = JSON.parse(saved);
        console.log('✅ Loaded categories from localStorage');
        return data;
      }
    } catch (error) {
      console.error('Failed to load categories from localStorage:', error);
    }
    return null;
  }

  /**
   * Save categories to localStorage
   */
  saveCategoriestoStorage() {
    try {
      localStorage.setItem('electixir_categories', JSON.stringify(this.data));
      console.log('✅ Saved categories to localStorage');
    } catch (error) {
      console.error('Failed to save categories to localStorage:', error);
    }
  }

  /**
   * Initialize filter state
   * @returns {Object} - Filter state object
   */
  initializeFilters() {
    return {
      gender: 'All',
      ageFrom: 18,
      ageTo: 120,
      community: 'All',
      religion: 'All',
      economicStatus: 'All',
      searchTerm: ''
    };
  }

  /**
   * Initialize voter data (placeholder for future backend integration)
   * @returns {Array} - Array of voter objects
   */
  initializeVoterData() {
    // This would be replaced with actual data fetching in production
    return [
      {
        id: 1,
        name: 'Rajesh Kumar',
        age: 45,
        gender: 'Male',
        epic: '**********',
        pollingStation: 'PS 1 - Central School',
        status: 'Active',
        community: 'General',
        religion: 'Hindu',
        economicStatus: 'Middle Income'
      },
      // ... more voter data would be here
    ];
  }

  /**
   * Get data for a specific category
   * @param {string} category - Category name
   * @returns {Array} - Array of items in category
   */
  getCategoryData(category) {
    return this.data[category] || [];
  }

  /**
   * Load categories from database
   */
  async loadCategoriesFromDatabase() {
    if (!this.settingsService) return;

    try {
      const categories = ['community', 'religion', 'economic_status'];
      for (const category of categories) {
        const items = await this.settingsService.getCategorySettings(category);
        this.data[category] = items;
      }
      console.log('✅ Loaded categories from database');
    } catch (error) {
      console.error('Failed to load categories from database:', error);
    }
  }

  /**
   * Add item to a category
   * @param {string} category - Category name
   * @param {string} item - Item to add
   * @returns {boolean} - Success status
   */
  async addCategoryItem(category, item) {
    if (!this.data[category]) {
      console.warn(`Category ${category} does not exist`);
      return false;
    }

    const trimmedItem = item.trim();
    if (!trimmedItem) {
      console.warn('Item cannot be empty');
      return false;
    }

    if (this.data[category].includes(trimmedItem)) {
      console.warn(`Item ${trimmedItem} already exists in ${category}`);
      return false;
    }

    // Add to database if available
    if (this.settingsService && this.isInitialized) {
      const success = await this.settingsService.addCategorySetting(category, trimmedItem);
      if (success) {
        this.data[category].push(trimmedItem);
        return true;
      }
      return false;
    } else {
      // Fallback to in-memory storage
      this.data[category].push(trimmedItem);
      return true;
    }
  }

  /**
   * Remove item from a category
   * @param {string} category - Category name
   * @param {string} item - Item to remove
   * @returns {boolean} - Success status
   */
  async removeCategoryItem(category, item) {
    if (!this.data[category]) {
      console.warn(`Category ${category} does not exist`);
      return false;
    }

    const index = this.data[category].indexOf(item);
    if (index === -1) {
      console.warn(`Item ${item} not found in ${category}`);
      return false;
    }

    // Remove from database if available
    if (this.settingsService && this.isInitialized) {
      const success = await this.settingsService.removeCategorySetting(category, item);
      if (success) {
        this.data[category].splice(index, 1);
        return true;
      }
      return false;
    } else {
      // Fallback to in-memory storage
      this.data[category].splice(index, 1);
      return true;
    }
  }

  /**
   * Update item in a category
   * @param {string} category - Category name
   * @param {string} oldItem - Current item name
   * @param {string} newItem - New item name
   * @returns {boolean} - Success status
   */
  async updateCategoryItem(category, oldItem, newItem) {
    if (!this.data[category]) {
      console.warn(`Category ${category} does not exist`);
      return false;
    }

    const index = this.data[category].indexOf(oldItem);
    if (index === -1) {
      console.warn(`Item ${oldItem} not found in ${category}`);
      return false;
    }

    const trimmedNewItem = newItem.trim();
    if (!trimmedNewItem) {
      console.warn('New item cannot be empty');
      return false;
    }

    if (this.data[category].includes(trimmedNewItem) && trimmedNewItem !== oldItem) {
      console.warn(`Item ${trimmedNewItem} already exists in ${category}`);
      return false;
    }

    // Update in database if available
    if (this.settingsService && this.isInitialized) {
      const success = await this.settingsService.updateCategorySetting(category, oldItem, trimmedNewItem);
      if (success) {
        this.data[category][index] = trimmedNewItem;
        return true;
      }
      return false;
    } else {
      // Fallback to in-memory storage
      this.data[category][index] = trimmedNewItem;
      return true;
    }
  }

  /**
   * Get formatted category name for display
   * @param {string} category - Category name
   * @returns {string} - Formatted category name
   */
  getFormattedCategoryName(category) {
    return formatCategoryName(category);
  }

  /**
   * Get all available categories
   * @returns {Array} - Array of category names
   */
  getCategories() {
    return Object.keys(this.data);
  }

  /**
   * Update filter value
   * @param {string} filterName - Filter name
   * @param {*} value - Filter value
   */
  updateFilter(filterName, value) {
    if (this.filters.hasOwnProperty(filterName)) {
      this.filters[filterName] = value;
    }
  }

  /**
   * Get current filter values
   * @returns {Object} - Current filter state
   */
  getFilters() {
    return { ...this.filters };
  }

  /**
   * Clear all filters to default values
   */
  clearFilters() {
    this.filters = this.initializeFilters();
  }

  /**
   * Get filtered voter data based on current filters
   * @returns {Array} - Filtered voter data
   */
  getFilteredVoterData() {
    return this.voterData.filter(voter => {
      // Apply gender filter
      if (this.filters.gender !== 'All' && voter.gender !== this.filters.gender) {
        return false;
      }

      // Apply age filter
      if (voter.age < this.filters.ageFrom || voter.age > this.filters.ageTo) {
        return false;
      }

      // Apply community filter
      if (this.filters.community !== 'All' && voter.community !== this.filters.community) {
        return false;
      }

      // Apply religion filter
      if (this.filters.religion !== 'All' && voter.religion !== this.filters.religion) {
        return false;
      }

      // Apply economic status filter
      if (this.filters.economicStatus !== 'All' && voter.economicStatus !== this.filters.economicStatus) {
        return false;
      }

      // Apply search term filter
      if (this.filters.searchTerm) {
        const searchTerm = this.filters.searchTerm.toLowerCase();
        return voter.name.toLowerCase().includes(searchTerm) ||
               voter.epic.toLowerCase().includes(searchTerm) ||
               voter.pollingStation.toLowerCase().includes(searchTerm);
      }

      return true;
    });
  }

  /**
   * Get voter statistics
   * @returns {Object} - Statistics object
   */
  getStatistics() {
    const filteredData = this.getFilteredVoterData();
    const total = filteredData.length;

    const maleCount = filteredData.filter(v => v.gender === 'Male').length;
    const femaleCount = filteredData.filter(v => v.gender === 'Female').length;
    const activeCount = filteredData.filter(v => v.status === 'Active').length;

    return {
      total,
      male: maleCount,
      female: femaleCount,
      active: activeCount,
      malePercentage: total > 0 ? ((maleCount / total) * 100).toFixed(1) : 0,
      femalePercentage: total > 0 ? ((femaleCount / total) * 100).toFixed(1) : 0
    };
  }
}

// Export singleton instance
export const dataManager = new DataManager();