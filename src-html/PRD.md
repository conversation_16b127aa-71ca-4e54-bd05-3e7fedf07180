# HTML-to-React Migration Plan

## Phase 1: Create Monolithic Component

1. Set up React project and initialize git
2. Create single component containing your entire HTML structure
3. Only convert: `class` → `className`, `for` → `htmlFor`, self-close void elements
4. Copy CSS files exactly as-is with no modifications
5. **Git commit**: "Phase 1: Convert HTML to single monolithic React component"

## Phase 2: Break into Components

1. Identify natural HTML boundaries (header, nav, main, footer, sections)
2. Extract one component at a time by copy-pasting exact HTML sections
3. Preserve all class names, IDs, attributes, and element structure
4. Test after each extraction
5. **Git commit** after each component: "Phase 2: Extract [ComponentName] - structure preserved"

## Phase 3: Convert JavaScript

1. Audit existing JavaScript for event handlers, DOM manipulation, state management
2. Convert jQuery/vanilla JS patterns to React hooks and event handlers
3. Preserve all existing business logic and functionality exactly
4. Maintain same user experience and behavior
5. **Git commit** after each JS file: "Phase 3: Convert [filename] to React - logic preserved"

## Critical Rules

- **Never modify** HTML structure, CSS properties, or business logic
- **Only adapt** how code integrates with React
- **Copy-paste** exact content, don't reinterpret
- **Test thoroughly** after each phase
- **Commit frequently** to track changes


## Success Criteria

- Phase 1: Identical visual rendering
- Phase 2: Same combined output with logical components
- Phase 3: All functionality works exactly as original


All html, css, js files are located in src-html

For this phase you have the following tools at your disposal. Use it wisely to speed up development, get uptodated code samples and documentations and make efficent use of filesystem.

  - `@modelcontextprotocol/server-filesystem` - Secure file operations with configurable access controls
  - `@modelcontextprotocol/server-git` - Tools to read, search, and manipulate Git repositories
  - `context7` - Up-to-date documentation access for Vue.js, Tauri, and any related frameworks

**IMPORTANT**
Once this phase is complete I will provide you a detailed database requirements MD file. Alsway use pnpm dev to run the project. We will use pnpm tauri at a later stage.