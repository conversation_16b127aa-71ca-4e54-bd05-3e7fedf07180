<!-- index.html -->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Voter Management Dashboard</title>
  <link rel="stylesheet" href="assets/normalize.css">
  <link rel="stylesheet" href="assets/styles.css">
</head>

<body>
  <aside class="sidebar" aria-label="Main navigation">
    <header class="sidebar-header">
      <div class="sidebar-title">Polling Stations</div>
    </header>
    <nav class="sidebar-content">
      <section class="sidebar-section">
        <ul class="tree">
          <li>
            <details open>
              <summary>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">1 - CENTRAL PARK</span>
                </label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </summary>
              <ul>
                <li>
                  <label class="checkbox-container child">
                    <input type="checkbox" checked>
                    <span class="checkmark"></span>
                    <span class="checkbox-label">1-Main Hall</span>
                  </label>
                </li>
                <li>
                  <label class="checkbox-container child">
                    <input type="checkbox" checked>
                    <span class="checkmark"></span>
                    <span class="checkbox-label">2-Community Room</span>
                  </label>
                </li>
                <li>
                  <label class="checkbox-container child">
                    <input type="checkbox" checked>
                    <span class="checkmark"></span>
                    <span class="checkbox-label">Unassigned</span>
                  </label>
                </li>
              </ul>
            </details>
          </li>
          <li>
            <details>
              <summary>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">2 - RIVERBANK SCHOOL</span>
                </label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </summary>
            </details>
          </li>
          <li>
            <details>
              <summary>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">3 - HILLTOP LIBRARY</span>
                </label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </summary>
            </details>
          </li>
          <li>
            <details>
              <summary>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">4 - LAKEVIEW CENTER</span>
                </label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </summary>
            </details>
          </li>
          <li>
            <details>
              <summary>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">5 - OAKWOOD HALL</span>
                </label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </summary>
            </details>
          </li>
          <li>
            <details>
              <summary>
                <label class="checkbox-container">
                  <input type="checkbox" checked>
                  <span class="checkmark"></span>
                  <span class="checkbox-label">6 - SUNRISE PLAZA</span>
                </label>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </summary>
            </details>
          </li>
        </ul>
      </section>
    </nav>
    <footer class="sidebar-footer">
      <section class="sidebar-section">
        <div class="sidebar-title">Management</div>
        <a href="#" class="sidebar-item" id="reports-btn">
          <span class="icon" aria-hidden="true">
            <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z"/><path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
            </svg>
          </span>
          <span>Reports</span>
        </a>
        <div class="dropdown-container">
          <a href="#" class="sidebar-item" id="settings-btn">
            <span class="icon" aria-hidden="true">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="3"></circle>
                <path
                  d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
              </svg>
            </span>
            <span>Settings</span>
          </a>
          <div class="dropdown-menu dropdown-menu-up" id="settings-dropdown">
            <div class="dropdown-item" id="manage-data-btn">
              <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 7V4h16v3"/>
                <path d="M9 20h6"/>
                <path d="M12 4v16"/>
              </svg>
              <span>Manage Data</span>
            </div>
            <div class="dropdown-item">
              <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 15V3"/>
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <path d="m7 10 5 5 5-5"/>
              </svg>
              <span>Import CSV</span>
            </div>
            <div class="dropdown-item">
              <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" >
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/><path d="M14 2v4a2 2 0 0 0 2 2h4"/><path d="M10 9H8"/><path d="M16 13H8"/><path d="M16 17H8"/>
              </svg>
              <span>Export PDF</span>
            </div>
            <div class="dropdown-item">
              <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m16 17 5-5-5-5"/><path d="M21 12H9"/><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
              </svg>
              <span>Logout</span>
            </div>
          </div>
        </div>
      </section>
    </footer>
    <div class="footer-summary">
      <small>6 Polling Stations • All Selected</small>
    </div>
  </aside>

  <main class="main-content">
    <div id="main-view">
      <div class="toolbar">
        <div class="toolbar-left">
          <h1>Voter Database</h1>
        </div>
      <div class="toolbar-right">
        <div class="theme-toggle">
          <button id="themeToggle" class="btn" aria-label="Toggle theme">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
            Light
          </button>
        </div>
        <button class="btn filter-btn" id="filter-btn" aria-label="Show filter options">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
          Filter
        </button>
        <button class="btn btn-primary" aria-label="Add voter">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Add Voter
        </button>
      </div>
    </div>

    <div class="stats-container">
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">Male Voters</span>
          <div class="stat-icon blue">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 3h5v5" />
              <path d="m21 3-6.75 6.75" />
              <circle cx="10" cy="14" r="6" />
            </svg>
          </div>
        </div>
        <div class="stat-value">1,222</div>
        <div class="stat-change positive">
          <span>40.6% of total</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">Female Voters</span>
          <div class="stat-icon green">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 15v7" />
              <path d="M9 19h6" />
              <circle cx="12" cy="9" r="6" />
            </svg>
          </div>
        </div>
        <div class="stat-value">1,789</div>
        <div class="stat-change positive">
          <span>59.4% of total</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">Households</span>
          <div class="stat-icon orange">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
          </div>
        </div>
        <div class="stat-value">1,234</div>
        <div class="stat-change positive">
          <span>Avg 2.4 voters</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">Total Voters</span>
          <div class="stat-icon red">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
        </div>
        <div class="stat-value">3,011</div>
        <div class="stat-change positive">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="19" x2="12" y2="5"></line>
            <polyline points="5 12 12 5 19 12"></polyline>
          </svg>
          <span>+2.1% this month</span>
        </div>
      </div>
    </div>

    <div class="filter-options-container">
      <div class="filter-options">
        <div class="filter-header">
          <div class="filter-title">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
            Filter Options
          </div>
          <button class="clear-filters" aria-label="Clear all filters">Clear Filters</button>
        </div>
        <div class="filter-controls">
          <div class="filter-section">
            <div class="filter-group">
              <label for="gender-select">Gender</label>
              <div class="select-wrapper">
                <select id="gender-select">
                  <option>All</option>
                  <option>Male</option>
                  <option>Female</option>
                </select>
              </div>
            </div>
          </div>
          <div class="filter-divider"></div>
          <div class="filter-section">
            <div class="filter-group">
              <label for="age-from">Age From</label>
              <input id="age-from" type="number" value="18" min="18" max="120">
            </div>
            <div class="filter-group">
              <label for="age-to">Age To</label>
              <input id="age-to" type="number" value="120" min="18" max="120">
            </div>
          </div>
          <div class="filter-divider"></div>
          <div class="filter-section">
            <div class="filter-group">
              <label for="community-select">Community</label>
              <div class="select-wrapper">
                <select id="community-select">
                  <option>All</option>
                  <option>General</option>
                  <option>OBC</option>
                  <option>SC</option>
                  <option>ST</option>
                </select>
              </div>
            </div>
            <div class="filter-group">
              <label for="religion-select">Religion</label>
              <div class="select-wrapper">
                <select id="religion-select">
                  <option>All</option>
                  <option>Hindu</option>
                  <option>Muslim</option>
                  <option>Christian</option>
                  <option>Sikh</option>
                  <option>Buddhist</option>
                  <option>Jain</option>
                  <option>Other</option>
                </select>
              </div>
            </div>
            <div class="filter-group">
              <label for="economic-select">Economic Status</label>
              <div class="select-wrapper">
                <select id="economic-select">
                  <option>All</option>
                  <option>APL</option>
                  <option>BPL</option>
                  <option>Middle Income</option>
                  <option>High Income</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="settings-dashboard-container">
      <div class="settings-dashboard">
        <div class="filter-header">
          <div class="filter-title">
            <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 7V4h16v3"/>
            <path d="M9 20h6"/>
            <path d="M12 4v16"/>
          </svg>
          Manage Data
        </div>
        <button class="panel-close" aria-label="Close panel">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      <div class="settings-controls">
        <div class="filter-group">
          <label for="category-select">Category</label>
          <div class="select-wrapper">
            <select id="category-select">
              <option value="community">Community</option>
              <option value="religion">Religion</option>
              <option value="economic_status">Economic Status</option>
            </select>
          </div>
        </div>
        <div class="add-item-form">
          <input type="text" id="new-item-input" placeholder="Enter new item">
          <button class="btn btn-primary" id="add-item-btn">Add</button>
        </div>
      </div>
      <div class="data-list">
        <h3>Community</h3>
        <ul id="item-list">
          <!-- Items will be dynamically added here -->
        </ul>
      </div>
     </div>
    </div>

    <div class="data-table">
      <div class="table-header">
        <span class="table-title">Registered Voters</span>
        <div class="table-controls">
          <div class="search-container">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor"
              stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
            <input type="text" class="search-input" placeholder="Search voters..." aria-label="Search voters">
          </div>
          <div class="dropdown-container">
            <button class="btn" id="columns-btn" aria-label="Show/hide columns">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="9" y1="9" x2="9" y2="21"></line>
                <line x1="15" y1="9" x2="15" y2="21"></line>
              </svg>
              Columns
            </button>
            <div class="dropdown-menu" id="columns-dropdown">
              <div class="dropdown-item is-selected">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Name</span>
              </div>
              <div class="dropdown-item is-selected">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Age</span>
              </div>
              <div class="dropdown-item is-selected">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Gender</span>
              </div>
              <div class="dropdown-item is-selected">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Epic</span>
              </div>
              <div class="dropdown-item is-selected">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Polling Station</span>
              </div>
              <div class="dropdown-item">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Affiliation</span>
              </div>
              <div class="dropdown-item">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Payee</span>
              </div>
              <div class="dropdown-item is-selected">
                <svg class="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6 9 17l-5-5" />
                </svg>
                <span>Status</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            <th>NAME</th>
            <th>AGE</th>
            <th>GENDER</th>
            <th>EPIC NUMBER</th>
            <th>POLLING STATION</th>
            <th>STATUS</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="name-cell">Rajesh Kumar</td>
            <td>45</td>
            <td><span class="gender-badge male">Male</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 1 - Central School</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
          <tr>
            <td class="name-cell">Priya Sharma</td>
            <td>32</td>
            <td><span class="gender-badge female">Female</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 2 - Community Center</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
          <tr>
            <td class="name-cell">Mohammed Ali</td>
            <td>28</td>
            <td><span class="gender-badge male">Male</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 1 - Central School</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
          <tr>
            <td class="name-cell">Sunita Devi</td>
            <td>52</td>
            <td><span class="gender-badge female">Female</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 3 - Town Hall</td>
            <td><span class="status-indicator status-inactive"></span>Expired</td>
          </tr>
          <tr>
            <td class="name-cell">Amit Patel</td>
            <td>38</td>
            <td><span class="gender-badge male">Male</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 1 - Central School</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
          <tr>
            <td class="name-cell">Neha Verma</td>
            <td>41</td>
            <td><span class="gender-badge female">Female</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 2 - Community Center</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
          <tr>
            <td class="name-cell">Ravi Joshi</td>
            <td>36</td>
            <td><span class="gender-badge male">Male</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 3 - Town Hall</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
          <tr>
            <td class="name-cell">Fatima Khan</td>
            <td>44</td>
            <td><span class="gender-badge female">Female</span></td>
            <td><span class="epic-code">**********</span></td>
            <td>PS 2 - Community Center</td>
            <td><span class="status-indicator status-active"></span>Active</td>
          </tr>
        </tbody>
      </table>
      <div class="table-footer">
        <div class="table-info">Showing 1-8 of 3,011 entries</div>
        <div class="pagination">
          <button class="page-btn" disabled aria-label="Previous page">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m15 18-6-6 6-6" />
            </svg>
          </button>
          <button class="page-btn is-active" aria-current="page">1</button>
          <button class="page-btn">2</button>
          <button class="page-btn">3</button>
          <button class="page-btn">4</button>
          <button class="page-btn">5</button>
          <button class="page-btn" aria-label="Next page">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
    </div>
    <div id="reports-view" style="display: none;">
      <div class="toolbar">
        <h1>Reports</h1>
        <div class="toolbar-left">
          <button id="back-to-main" class="btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m15 18-6-6 6-6"/>
            </svg>
            Back
          </button>
        </div>
      </div>
      <div class="reports-content">
        <!-- Placeholder for charts -->
        <p>Chart placeholder</p>
      </div>
    </div>
  </main>

  <!-- Off-canvas Voter Detail Panel -->
  <div class="voter-detail-panel">
    <div class="panel-header">
      <h2 class="panel-title">Voter Details</h2>
      <div class="panel-actions">
        <button class="btn btn-edit">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          Edit
        </button>
        <button class="btn btn-primary btn-save" style="display: none;">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
            <polyline points="17 21 17 13 7 13 7 21"></polyline>
            <polyline points="7 3 7 8 15 8"></polyline>
          </svg>
          Save
        </button>
        <button class="panel-close" aria-label="Close panel">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>

    <div class="panel-content">
      <!-- Voter Profile Section -->
      <section class="panel-section">
        <details open>
          <summary>
            <h3 class="section-title">Voter Profile</h3>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6"></path>
            </svg>
          </summary>
          <div class="section-grid">
            <div class="detail-item">
              <span class="detail-label">Voter's Name</span>
              <span class="detail-value" id="detail-name">Rajesh Kumar</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">EPIC Number</span>
              <span class="detail-value" id="detail-epic">**********</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Father's Name</span>
              <span class="detail-value" id="detail-relation">Ramesh Kumar</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">House Number</span>
              <span class="detail-value" id="detail-address">123</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Age</span>
              <span class="detail-value" id="detail-age">45</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Gender</span>
              <span class="detail-value" id="detail-gender">Male</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Polling Station</span>
              <span class="detail-value" id="detail-station">1 - Amjong</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Section</span>
              <span class="detail-value" id="detail-section">1-Amjong</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Voter Status</span>
              <span class="detail-value" id="detail-status">Active</span>
            </div>
          </div>
        </details>
      </section>

      <!-- Combined Section for Status, Demographics and Notes -->
      <section class="panel-section">
        <details>
          <summary>
            <h3 class="section-title">Additional Information</h3>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6"></path>
            </svg>
          </summary>
          <!-- Status Section -->
          <div class="panel-subsection">
            <h4 class="subsection-title">Voter Affiliation</h4>
            <div class="status-grid">
              <div class="status-item">
                <span class="status-indicator"></span>
                <span class="status-label">Support Status</span>
                <span class="status-value">Unknown</span>
              </div>
            </div>
          </div>

          <!-- Demographics Section -->
          <div class="panel-subsection">
            <h4 class="subsection-title">Demographics</h4>
            <div class="section-grid">
              <div class="detail-item">
                <span class="detail-label">Education</span>
                <span class="detail-value" id="detail-education">Bachelor's Degree</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Qualification</span>
                <span class="detail-value" id="detail-qualification">Certified Accountant</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Community</span>
                <span class="detail-value" id="detail-community">General</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Religion</span>
                <span class="detail-value" id="detail-religion">Hindu</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Economic Status</span>
                <span class="detail-value" id="detail-economic">Middle Income</span>
              </div>
            </div>
          </div>

          <!-- Notes Section -->
          <div class="panel-subsection">
            <div class="section-header">
              <h4 class="subsection-title">Notes</h4>
              <button class="btn btn-small">Add Note</button>
            </div>
            <div class="notes-content">
              <p class="note-text">Regular voter, attends all meetings. Prefers evening visits.</p>
            </div>
          </div>
        </details>
      </section>

      <!-- Contact Details Section -->
      <section class="panel-section">
        <details>
          <summary>
            <h3 class="section-title">Contact Details</h3>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="m9 18 6-6-6-6"></path>
            </svg>
          </summary>
          <div class="section-grid">
            <div class="detail-item">
              <span class="detail-label">Phone</span>
              <span class="detail-value" id="detail-phone">9876543210</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Email</span>
              <span class="detail-value" id="detail-email"><EMAIL></span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Facebook</span>
              <span class="detail-value" id="detail-facebook">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Instagram</span>
              <span class="detail-value" id="detail-instagram">-</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Twitter</span>
              <span class="detail-value" id="detail-twitter">-</span>
            </div>
          </div>
        </details>
      </section>

      <!-- Family Members Section -->
      <section class="panel-section">
        <details>
          <summary>
            <h3 class="section-title">Family Members</h3>
            <div class="summary-right">
              <span class="badge badge-count">2 members</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>
          </summary>
          <div class="family-members">
            <div class="family-member">
              <span class="member-name">Ramesh Kumar</span>
              <span class="member-relation">Father</span>
            </div>
            <div class="family-member">
              <span class="member-name">Sita Devi</span>
              <span class="member-relation">Mother</span>
            </div>
          </div>
        </details>
      </section>

      <!-- Transactions Section -->
      <section class="panel-section">
        <details>
          <summary>
            <h3 class="section-title">Transactions</h3>
            <div class="summary-right">
              <span class="badge badge-amount">₹7,000</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>
          </summary>
          <div class="transactions-table">
            <table>
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Purpose</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>15/07/2025</td>
                  <td>Medical Assistance</td>
                  <td>₹5,000</td>
                </tr>
                <tr>
                  <td>02/05/2025</td>
                  <td>Festival Support</td>
                  <td>₹2,000</td>
                </tr>
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="2">Total</td>
                  <td>₹7,000</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </details>
      </section>
    </div>
  </div>

  <script type="module" src="assets/js/main.js"></script>
</body>

</html>
