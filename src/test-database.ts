// Simple database test script
import { DatabaseService, VoterService, CSVImportService } from './database';

export async function testDatabase() {
  console.log('🧪 Starting database test...');
  
  try {
    // Initialize database
    console.log('📦 Initializing database...');
    const dbService = DatabaseService.getInstance();
    await dbService.initialize({ wasmUrl: '' });
    console.log('✅ Database initialized successfully');

    // Test voter service
    console.log('👥 Testing voter service...');
    const voterService = new VoterService();
    
    // Add a test voter
    const testVoter = {
      name: 'Test User',
      gender: 'Male' as const,
      epic_number: 'TEST123456',
      birth_year: 1990,
      polling_station: 'Test Station',
      section: 'Test Section',
      status: 'Active' as const
    };
    
    const voterId = await voterService.addVoter(testVoter);
    console.log(`✅ Added test voter with ID: ${voterId}`);
    
    // Get all voters
    const voters = await voterService.getVoters();
    console.log(`✅ Retrieved ${voters.length} voters`);
    
    // Test CSV import
    console.log('📄 Testing CSV import...');
    const csvService = new CSVImportService();
    
    // Create a simple CSV string
    const csvData = `name,relation_type,relation_name,house_number,birth_year,gender,epic_number,polling_station,section
John Doe,Father,Robert Doe,123,1985,Male,ABC123,Station 1,Section A
Jane Smith,Mother,Mary Smith,456,1990,Female,DEF456,Station 2,Section B`;
    
    // Test CSV validation
    const file = new File([csvData], 'test.csv', { type: 'text/csv' });
    const validation = await csvService.validateCSVStructure(file);
    console.log('✅ CSV validation:', validation);
    
    if (validation.valid) {
      const importResult = await csvService.importFromString(csvData);
      console.log('✅ CSV import result:', importResult);
    }
    
    // Get updated voter count
    const updatedVoters = await voterService.getVoters();
    console.log(`✅ Total voters after import: ${updatedVoters.length}`);
    
    // Test export
    const exportedCsv = await csvService.exportToCSV();
    console.log('✅ Exported CSV length:', exportedCsv.length);
    
    console.log('🎉 All database tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    return false;
  }
}

// Make it available globally for browser console testing
(window as any).testDatabase = testDatabase;
