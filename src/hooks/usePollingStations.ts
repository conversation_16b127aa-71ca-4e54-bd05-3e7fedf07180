import { useState, useCallback, useEffect } from 'react';
import { useApp } from '../context/AppContext';

export interface PollingStationSection {
  id: string;
  name: string;
  isSelected: boolean;
  voterCount: number;
}

export interface PollingStation {
  id: string;
  name: string;
  isOpen: boolean;
  isSelected: boolean;
  sections: PollingStationSection[];
  voterCount: number;
}

export function usePollingStations() {
  const { state, dispatch } = useApp();
  const { voters } = state;
  const [pollingStations, setPollingStations] = useState<PollingStation[]>([]);

  // Build polling stations from voter data
  useEffect(() => {
    const stationMap = new Map<string, { sections: Map<string, number>; totalVoters: number }>();

    // Group voters by polling station and section
    voters.forEach(voter => {
      const stationName = voter.polling_station || voter.pollingStation || 'Unassigned';
      const sectionName = voter.section || 'Unassigned';

      if (!stationMap.has(stationName)) {
        stationMap.set(stationName, { sections: new Map(), totalVoters: 0 });
      }

      const station = stationMap.get(stationName)!;
      station.totalVoters++;

      const currentSectionCount = station.sections.get(sectionName) || 0;
      station.sections.set(sectionName, currentSectionCount + 1);
    });

    // Convert to PollingStation array
    const newPollingStations: PollingStation[] = Array.from(stationMap.entries()).map(([stationName, stationData], index) => {
      const sections: PollingStationSection[] = Array.from(stationData.sections.entries()).map(([sectionName, voterCount], sectionIndex) => ({
        id: `${stationName}_${sectionName}`,
        name: sectionName,
        isSelected: true,
        voterCount
      }));

      return {
        id: stationName,
        name: stationName,
        isOpen: index === 0, // First station open by default
        isSelected: true,
        sections: sections.sort((a, b) => a.name.localeCompare(b.name)),
        voterCount: stationData.totalVoters
      };
    });

    // Sort stations by name
    newPollingStations.sort((a, b) => a.name.localeCompare(b.name));
    setPollingStations(newPollingStations);
  }, [voters]);

  const toggleStationOpen = useCallback((stationId: string) => {
    setPollingStations(prev => prev.map(station =>
      station.id === stationId
        ? { ...station, isOpen: !station.isOpen }
        : station
    ));
  }, []);

  const toggleStationSelected = useCallback((stationId: string) => {
    setPollingStations(prev => {
      const newStations = prev.map(station =>
        station.id === stationId
          ? {
              ...station,
              isSelected: !station.isSelected,
              sections: station.sections.map(section => ({
                ...section,
                isSelected: !station.isSelected
              }))
            }
          : station
      );

      // Update filters based on selection
      const selectedStations = newStations.filter(s => s.isSelected).map(s => s.id);
      const selectedSections = newStations.flatMap(s =>
        s.sections.filter(sec => sec.isSelected).map(sec => sec.id)
      );

      dispatch({ type: 'UPDATE_SELECTED_POLLING_STATIONS', payload: selectedStations });
      dispatch({ type: 'UPDATE_SELECTED_SECTIONS', payload: selectedSections });

      return newStations;
    });
  }, [dispatch]);

  const toggleSectionSelected = useCallback((stationId: string, sectionId: string) => {
    setPollingStations(prev => {
      const newStations = prev.map(station =>
        station.id === stationId
          ? {
              ...station,
              sections: station.sections.map(section =>
                section.id === sectionId
                  ? { ...section, isSelected: !section.isSelected }
                  : section
              )
            }
          : station
      );

      // Update filters based on selection
      const selectedStations = newStations.filter(s => s.isSelected).map(s => s.id);
      const selectedSections = newStations.flatMap(s =>
        s.sections.filter(sec => sec.isSelected).map(sec => sec.id)
      );

      dispatch({ type: 'UPDATE_SELECTED_POLLING_STATIONS', payload: selectedStations });
      dispatch({ type: 'UPDATE_SELECTED_SECTIONS', payload: selectedSections });

      return newStations;
    });
  }, [dispatch]);

  const getSelectedCount = useCallback(() => {
    return pollingStations.filter(station => station.isSelected).length;
  }, [pollingStations]);

  const getSelectedSectionsCount = useCallback(() => {
    return pollingStations.reduce((total, station) =>
      total + station.sections.filter(section => section.isSelected).length, 0
    );
  }, [pollingStations]);

  return {
    pollingStations,
    toggleStationOpen,
    toggleStationSelected,
    toggleSectionSelected,
    getSelectedCount,
    getSelectedSectionsCount,
    totalStations: pollingStations.length
  };
}