import { useCallback } from 'react';

/**
 * Custom hook for triggering light sweep animation
 * @returns function to trigger the animation on an element
 */
export function useLightSweep() {
  const triggerLightSweep = useCallback((element: HTMLElement | null) => {
    if (!element) return;

    // Remove existing animation class
    element.classList.remove('animate-sweep');
    
    // Force reflow to ensure the class removal takes effect
    void element.offsetWidth;

    // Add animation class with a small delay
    setTimeout(() => {
      element.classList.add('animate-sweep');
      
      // Remove the class after animation completes
      setTimeout(() => {
        element.classList.remove('animate-sweep');
      }, 1500); // Match the animation duration
    }, 10);
  }, []);

  return triggerLightSweep;
}
