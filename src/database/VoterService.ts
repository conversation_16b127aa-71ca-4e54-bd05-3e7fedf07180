import { Database } from 'sql.js';
import DatabaseService from './DatabaseService';

export interface VoterData {
  id?: number;
  name: string;
  relationship_type?: 'Father' | 'Mother' | 'Husband' | 'Others';
  relationship_name?: string;
  gender: 'Male' | 'Female' | 'Other';
  birth_year?: number;
  epic_number: string;
  house_number?: string;
  polling_station?: string;
  section?: string;

  // Extended contact information
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;

  // Voter status
  status?: 'Active' | 'Expired' | 'Shifted' | 'Duplicate' | 'Missing' | 'Disqualified';

  // Political information
  supporter_status?: 'Strong Supporter' | 'Potential Supporter' | 'Undecided' | 'Opposed';

  // Demographics
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  custom_notes?: string;
}

export interface VoterFilters {
  searchTerm?: string;
  gender?: string;
  status?: string;
  polling_station?: string;
  section?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  supporter_status?: string;
  ageFrom?: number;
  ageTo?: number;
}

export class VoterService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = DatabaseService.getInstance();
  }

  private getDatabase(): Database {
    return this.dbService.getDatabase();
  }

  /**
   * Add a new voter or update existing one based on epic_number
   */
  public async upsertVoter(voterData: VoterData): Promise<{ id: number; isNew: boolean }> {
    const db = this.getDatabase();

    try {
      // Check if voter with this epic_number already exists
      const existingStmt = db.prepare('SELECT id FROM voters WHERE epic_number = ?');
      existingStmt.bind([voterData.epic_number]);

      let existingId: number | null = null;
      if (existingStmt.step()) {
        const result = existingStmt.getAsObject();
        existingId = result.id as number;
      }
      existingStmt.free();

      if (existingId) {
        // Update existing voter
        await this.updateVoter(existingId, voterData);
        return { id: existingId, isNew: false };
      } else {
        // Add new voter
        const newId = await this.addVoter(voterData);
        return { id: newId, isNew: true };
      }
    } catch (error) {
      console.error('Failed to upsert voter:', error);
      throw error;
    }
  }

  /**
   * Add a new voter
   */
  public async addVoter(voterData: VoterData): Promise<number> {
    const db = this.getDatabase();

    try {
      // First, ensure polling station exists
      let pollingStationId: number | null = null;
      if (voterData.polling_station) {
        pollingStationId = await this.ensurePollingStationExists(voterData.polling_station);
      }

      // Then, ensure section exists
      let sectionId: number | null = null;
      if (voterData.section && pollingStationId) {
        sectionId = await this.ensureSectionExists(voterData.section, pollingStationId);
      }

      const stmt = db.prepare(`
        INSERT INTO voters (
          name, relationship_type, relationship_name, gender, birth_year, epic_number,
          house_number, polling_station_id, section_id, phone, email, facebook,
          instagram, twitter, status, supporter_status, education, occupation,
          community, religion, economic_status, custom_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run([
        voterData.name,
        voterData.relationship_type || null,
        voterData.relationship_name || null,
        voterData.gender,
        voterData.birth_year || null,
        voterData.epic_number,
        voterData.house_number || null,
        pollingStationId,
        sectionId,
        voterData.phone || null,
        voterData.email || null,
        voterData.facebook || null,
        voterData.instagram || null,
        voterData.twitter || null,
        voterData.status || 'Active',
        voterData.supporter_status || null,
        voterData.education || null,
        voterData.occupation || null,
        voterData.community || null,
        voterData.religion || null,
        voterData.economic_status || null,
        voterData.custom_notes || null
      ]);

      stmt.free();

      // Get the inserted ID
      const result = db.exec("SELECT last_insert_rowid() as id");
      const voterId = result[0].values[0][0] as number;

      // Save to localStorage
      this.dbService.saveToLocalStorage();

      return voterId;
    } catch (error) {
      console.error('Failed to add voter:', error);
      throw error;
    }
  }

  /**
   * Get all voters with optional filters
   */
  public async getVoters(filters: VoterFilters = {}): Promise<VoterData[]> {
    const db = this.getDatabase();

    try {
      let sql = `
        SELECT
          v.*,
          ps.name as polling_station,
          s.name as section
        FROM voters v
        LEFT JOIN polling_stations ps ON v.polling_station_id = ps.id
        LEFT JOIN sections s ON v.section_id = s.id
        WHERE 1=1
      `;

      const params: any[] = [];

      // Apply filters
      if (filters.searchTerm) {
        sql += ` AND (v.name LIKE ? OR v.epic_number LIKE ?)`;
        params.push(`%${filters.searchTerm}%`, `%${filters.searchTerm}%`);
      }

      if (filters.gender && filters.gender !== 'All') {
        sql += ` AND v.gender = ?`;
        params.push(filters.gender);
      }

      if (filters.status && filters.status !== 'All') {
        sql += ` AND v.status = ?`;
        params.push(filters.status);
      }

      if (filters.polling_station && filters.polling_station !== 'All') {
        sql += ` AND ps.name = ?`;
        params.push(filters.polling_station);
      }

      if (filters.section && filters.section !== 'All') {
        sql += ` AND s.name = ?`;
        params.push(filters.section);
      }

      if (filters.community && filters.community !== 'All') {
        sql += ` AND v.community = ?`;
        params.push(filters.community);
      }

      if (filters.religion && filters.religion !== 'All') {
        sql += ` AND v.religion = ?`;
        params.push(filters.religion);
      }

      if (filters.economic_status && filters.economic_status !== 'All') {
        sql += ` AND v.economic_status = ?`;
        params.push(filters.economic_status);
      }

      if (filters.supporter_status && filters.supporter_status !== 'All') {
        sql += ` AND v.supporter_status = ?`;
        params.push(filters.supporter_status);
      }

      // Age filters
      if (filters.ageFrom !== undefined) {
        const currentYear = new Date().getFullYear();
        const maxBirthYear = currentYear - filters.ageFrom;
        sql += ` AND v.birth_year <= ?`;
        params.push(maxBirthYear);
      }

      if (filters.ageTo !== undefined) {
        const currentYear = new Date().getFullYear();
        const minBirthYear = currentYear - filters.ageTo;
        sql += ` AND v.birth_year >= ?`;
        params.push(minBirthYear);
      }

      sql += ` ORDER BY v.name`;

      const stmt = db.prepare(sql);
      const result: VoterData[] = [];

      stmt.bind(params);
      while (stmt.step()) {
        const row = stmt.getAsObject();
        result.push({
          id: row.id as number,
          name: row.name as string,
          relationship_type: row.relationship_type as any,
          relationship_name: row.relationship_name as string,
          gender: row.gender as any,
          birth_year: row.birth_year as number,
          epic_number: row.epic_number as string,
          house_number: row.house_number as string,
          polling_station: row.polling_station as string,
          section: row.section as string,
          phone: row.phone as string,
          email: row.email as string,
          facebook: row.facebook as string,
          instagram: row.instagram as string,
          twitter: row.twitter as string,
          status: row.status as any,
          supporter_status: row.supporter_status as any,
          education: row.education as string,
          occupation: row.occupation as string,
          community: row.community as string,
          religion: row.religion as string,
          economic_status: row.economic_status as string,
          custom_notes: row.custom_notes as string
        });
      }

      stmt.free();
      return result;
    } catch (error) {
      console.error('Failed to get voters:', error);
      throw error;
    }
  }

  /**
   * Get voter by ID
   */
  public async getVoterById(id: number): Promise<VoterData | null> {
    const voters = await this.getVoters();
    return voters.find(v => v.id === id) || null;
  }

  /**
   * Update voter
   */
  public async updateVoter(id: number, voterData: Partial<VoterData>): Promise<void> {
    const db = this.getDatabase();

    try {
      // Handle polling station and section updates
      let pollingStationId: number | null = null;
      let sectionId: number | null = null;

      if (voterData.polling_station) {
        pollingStationId = await this.ensurePollingStationExists(voterData.polling_station);
      }

      if (voterData.section && pollingStationId) {
        sectionId = await this.ensureSectionExists(voterData.section, pollingStationId);
      }

      const updateFields: string[] = [];
      const params: any[] = [];

      // Build dynamic update query
      Object.entries(voterData).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'polling_station' && key !== 'section') {
          updateFields.push(`${key} = ?`);
          params.push(value);
        }
      });

      if (pollingStationId !== null) {
        updateFields.push('polling_station_id = ?');
        params.push(pollingStationId);
      }

      if (sectionId !== null) {
        updateFields.push('section_id = ?');
        params.push(sectionId);
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      params.push(id);

      const sql = `UPDATE voters SET ${updateFields.join(', ')} WHERE id = ?`;

      const stmt = db.prepare(sql);
      stmt.run(params);
      stmt.free();

      this.dbService.saveToLocalStorage();
    } catch (error) {
      console.error('Failed to update voter:', error);
      throw error;
    }
  }

  /**
   * Delete voter
   */
  public async deleteVoter(id: number): Promise<void> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('DELETE FROM voters WHERE id = ?');
      stmt.run([id]);
      stmt.free();

      this.dbService.saveToLocalStorage();
    } catch (error) {
      console.error('Failed to delete voter:', error);
      throw error;
    }
  }

  /**
   * Ensure polling station exists, create if not
   */
  private async ensurePollingStationExists(name: string): Promise<number> {
    const db = this.getDatabase();

    // Check if exists
    const checkStmt = db.prepare('SELECT id FROM polling_stations WHERE name = ?');
    checkStmt.bind([name]);

    if (checkStmt.step()) {
      const result = checkStmt.getAsObject();
      checkStmt.free();
      return result.id as number;
    }
    checkStmt.free();

    // Create new
    const insertStmt = db.prepare('INSERT INTO polling_stations (name) VALUES (?)');
    insertStmt.run([name]);
    insertStmt.free();

    const result = db.exec("SELECT last_insert_rowid() as id");
    return result[0].values[0][0] as number;
  }

  /**
   * Ensure section exists, create if not
   */
  private async ensureSectionExists(name: string, pollingStationId: number): Promise<number> {
    const db = this.getDatabase();

    // Check if exists
    const checkStmt = db.prepare('SELECT id FROM sections WHERE name = ? AND polling_station_id = ?');
    checkStmt.bind([name, pollingStationId]);

    if (checkStmt.step()) {
      const result = checkStmt.getAsObject();
      checkStmt.free();
      return result.id as number;
    }
    checkStmt.free();

    // Create new
    const insertStmt = db.prepare('INSERT INTO sections (name, polling_station_id) VALUES (?, ?)');
    insertStmt.run([name, pollingStationId]);
    insertStmt.free();

    const result = db.exec("SELECT last_insert_rowid() as id");
    return result[0].values[0] as number;
  }

  /**
   * Get all polling stations
   */
  public async getPollingStations(): Promise<string[]> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('SELECT name FROM polling_stations ORDER BY name');

      const result: string[] = [];
      while (stmt.step()) {
        const row = stmt.getAsObject();
        result.push(row.name as string);
      }

      stmt.free();
      return result;
    } catch (error) {
      console.error('Failed to get polling stations:', error);
      throw error;
    }
  }

  /**
   * Get all sections for a polling station
   */
  public async getSectionsForPollingStation(pollingStationName: string): Promise<string[]> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare(`
        SELECT s.name
        FROM sections s
        JOIN polling_stations ps ON s.polling_station_id = ps.id
        WHERE ps.name = ?
        ORDER BY s.name
      `);
      stmt.bind([pollingStationName]);

      const result: string[] = [];
      while (stmt.step()) {
        const row = stmt.getAsObject();
        result.push(row.name as string);
      }

      stmt.free();
      return result;
    } catch (error) {
      console.error('Failed to get sections for polling station:', error);
      throw error;
    }
  }

  /**
   * Get households grouped by relationship_name and house_number
   */
  public async getHouseholds(filters: VoterFilters = {}): Promise<Household[]> {
    const voters = await this.getVoters(filters);
    const householdMap = new Map<string, VoterData[]>();

    // Group voters by household key (relationship_name + house_number)
    voters.forEach(voter => {
      if (voter.relationship_name && voter.house_number) {
        const householdKey = `${voter.relationship_name.trim()}_${voter.house_number.trim()}`;
        if (!householdMap.has(householdKey)) {
          householdMap.set(householdKey, []);
        }
        householdMap.get(householdKey)!.push(voter);
      }
    });

    // Convert to household objects
    const households: Household[] = [];
    householdMap.forEach((members, key) => {
      if (members.length > 1) { // Only include households with multiple members
        const [relationshipName, houseNumber] = key.split('_');
        households.push({
          id: key,
          relationship_name: relationshipName,
          house_number: houseNumber,
          members: members,
          memberCount: members.length,
          polling_station: members[0].polling_station,
          section: members[0].section
        });
      }
    });

    // Sort by member count (largest households first)
    return households.sort((a, b) => b.memberCount - a.memberCount);
  }
}

export interface Household {
  id: string;
  relationship_name: string;
  house_number: string;
  members: VoterData[];
  memberCount: number;
  polling_station?: string;
  section?: string;
}

export default VoterService;
