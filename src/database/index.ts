// Database Services
export { default as DatabaseService } from './DatabaseService';
export { default as VoterService } from './VoterService';
export { default as CSVImportService } from './CSVImportService';

// Types
export type { DatabaseConfig } from './DatabaseService';
export type { VoterData, VoterFilters, Household } from './VoterService';
export type { CSVImportResult, CSVVoterRow } from './CSVImportService';

// Initialize database service
import DatabaseService from './DatabaseService';

export const initializeDatabase = async (config?: { wasmUrl?: string }) => {
  const dbService = DatabaseService.getInstance();
  await dbService.initialize(config);
  return dbService;
};
