import { Database } from 'sql.js';
import DatabaseService from './DatabaseService';

export interface SettingData {
  id?: number;
  category: string;
  value: string;
  created_at?: string;
}

export class SettingsService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = DatabaseService.getInstance();
  }

  /**
   * Get database instance
   */
  private getDatabase(): Database {
    return this.dbService.getDatabase();
  }

  /**
   * Get all settings for a category
   */
  public async getCategorySettings(category: string): Promise<string[]> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('SELECT value FROM settings WHERE category = ? ORDER BY value');
      stmt.bind([category]);

      const result: string[] = [];
      while (stmt.step()) {
        const row = stmt.getAsObject();
        result.push(row.value as string);
      }

      stmt.free();
      return result;
    } catch (error) {
      console.error('Failed to get category settings:', error);
      throw error;
    }
  }

  /**
   * Add a new setting to a category
   */
  public async addCategorySetting(category: string, value: string): Promise<boolean> {
    const db = this.getDatabase();

    try {
      const trimmedValue = value.trim();
      if (!trimmedValue) {
        console.warn('Setting value cannot be empty');
        return false;
      }

      // Check if the setting already exists
      const existingStmt = db.prepare('SELECT id FROM settings WHERE category = ? AND value = ?');
      existingStmt.bind([category, trimmedValue]);

      if (existingStmt.step()) {
        existingStmt.free();
        console.warn(`Setting ${trimmedValue} already exists in ${category}`);
        return false;
      }
      existingStmt.free();

      // Insert new setting
      const stmt = db.prepare('INSERT INTO settings (category, value) VALUES (?, ?)');
      stmt.run([category, trimmedValue]);
      stmt.free();

      // Save to localStorage
      this.dbService.saveToLocalStorage();

      return true;
    } catch (error) {
      console.error('Failed to add category setting:', error);
      return false;
    }
  }

  /**
   * Update a setting in a category
   */
  public async updateCategorySetting(category: string, oldValue: string, newValue: string): Promise<boolean> {
    const db = this.getDatabase();

    try {
      const trimmedNewValue = newValue.trim();
      if (!trimmedNewValue) {
        console.warn('New setting value cannot be empty');
        return false;
      }

      if (trimmedNewValue === oldValue) {
        return true; // No change needed
      }

      // Check if the new value already exists
      const existingStmt = db.prepare('SELECT id FROM settings WHERE category = ? AND value = ?');
      existingStmt.bind([category, trimmedNewValue]);

      if (existingStmt.step()) {
        existingStmt.free();
        console.warn(`Setting ${trimmedNewValue} already exists in ${category}`);
        return false;
      }
      existingStmt.free();

      // Update the setting
      const stmt = db.prepare('UPDATE settings SET value = ? WHERE category = ? AND value = ?');
      stmt.run([trimmedNewValue, category, oldValue]);
      stmt.free();

      // Save to localStorage
      this.dbService.saveToLocalStorage();

      return true;
    } catch (error) {
      console.error('Failed to update category setting:', error);
      return false;
    }
  }

  /**
   * Remove a setting from a category
   */
  public async removeCategorySetting(category: string, value: string): Promise<boolean> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('DELETE FROM settings WHERE category = ? AND value = ?');
      stmt.run([category, value]);
      stmt.free();

      // Save to localStorage
      this.dbService.saveToLocalStorage();

      return true;
    } catch (error) {
      console.error('Failed to remove category setting:', error);
      return false;
    }
  }

  /**
   * Initialize default settings if they don't exist
   */
  public async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = {
      'community': ['General', 'OBC', 'SC', 'ST'],
      'religion': ['Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain', 'Other'],
      'economic_status': ['APL', 'BPL', 'Middle Income', 'High Income'],
      'education': ['Primary', 'Secondary', 'Higher Secondary', 'Graduate', 'Post Graduate', 'Doctorate', 'Others'],
      'occupation': ['Student', 'Government Employee', 'Private Employee', 'Business/Self Employed', 'Farmer', 'Labor/Worker', 'Retired', 'Homemaker', 'Unemployed']
    };

    for (const [category, values] of Object.entries(defaultSettings)) {
      const existingValues = await this.getCategorySettings(category);
      
      if (existingValues.length === 0) {
        // Add default values if category is empty
        for (const value of values) {
          await this.addCategorySetting(category, value);
        }
        console.log(`✅ Initialized default settings for ${category}`);
      }
    }
  }

  /**
   * Get all categories that have settings
   */
  public async getAllCategories(): Promise<string[]> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('SELECT DISTINCT category FROM settings ORDER BY category');
      
      const result: string[] = [];
      while (stmt.step()) {
        const row = stmt.getAsObject();
        result.push(row.category as string);
      }

      stmt.free();
      return result;
    } catch (error) {
      console.error('Failed to get all categories:', error);
      throw error;
    }
  }
}

export default SettingsService;
