import React from 'react';
import PollingStationNode from './PollingStationNode';

interface Section {
  id: number;
  name: string;
  isSelected: boolean;
}

interface PollingStation {
  id: number;
  name: string;
  isOpen: boolean;
  isSelected: boolean;
  sections: Section[];
}

interface PollingStationListProps {
  pollingStations: PollingStation[];
  onToggle: (e: React.MouseEvent, stationId: number) => void;
  onStationSelect: (stationId: number) => void;
  onSectionSelect: (stationId: number, sectionId: number) => void;
}

const PollingStationList: React.FC<PollingStationListProps> = ({
  pollingStations,
  onToggle,
  onStationSelect,
  onSectionSelect,
}) => {
  return (
    <section className="sidebar-section">
      <ul className="tree">
        {pollingStations.map((station) => (
          <PollingStationNode
            key={station.id}
            station={station}
            onToggle={onToggle}
            onStationSelect={onStationSelect}
            onSectionSelect={onSectionSelect}
          />
        ))}
      </ul>
    </section>
  );
};

export default PollingStationList;
