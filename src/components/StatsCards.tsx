import { useApp } from "../context/AppContext";
import { useFilteredVoters, useVoterStats } from "../hooks/useFilteredVoters";

function StatsCards() {
  const { state } = useApp();
  const { voters, filters } = state;

  const filteredVoters = useFilteredVoters(voters, filters);
  const stats = useVoterStats(filteredVoters);

  return (
    <div className="stats-container">
      <div className="stat-card">
        <div className="stat-header">
          <span className="stat-label">Male Voters</span>
          <div className="stat-icon blue">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 3h5v5" />
              <path d="m21 3-6.75 6.75" />
              <circle cx="10" cy="14" r="6" />
            </svg>
          </div>
        </div>
        <div className="stat-value">{stats.male.toLocaleString()}</div>
        <div className="stat-change positive">
          <span>{stats.malePercentage}% of total</span>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-header">
          <span className="stat-label">Female Voters</span>
          <div className="stat-icon green">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 15v7" />
              <path d="M9 19h6" />
              <circle cx="12" cy="9" r="6" />
            </svg>
          </div>
        </div>
        <div className="stat-value">{stats.female.toLocaleString()}</div>
        <div className="stat-change positive">
          <span>{stats.femalePercentage}% of total</span>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-header">
          <span className="stat-label">Households</span>
          <div className="stat-icon orange">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
          </div>
        </div>
        <div className="stat-value">{stats.households.toLocaleString()}</div>
        <div className="stat-change positive">
          <span>Avg {stats.avgVotersPerHousehold} voters</span>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-header">
          <span className="stat-label">Total Voters</span>
          <div className="stat-icon red">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
        </div>
        <div className="stat-value">{stats.total.toLocaleString()}</div>
        <div className="stat-change positive">
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
            strokeLinecap="round" strokeLinejoin="round">
            <line x1="12" y1="19" x2="12" y2="5"></line>
            <polyline points="5 12 12 5 19 12"></polyline>
          </svg>
          <span>Filtered results</span>
        </div>
      </div>
    </div>
  );
}

export default StatsCards;
