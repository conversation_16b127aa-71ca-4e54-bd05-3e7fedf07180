import StatsCards from "./StatsCards";
import FilterPanel from "./FilterPanel";
import DataTable from "./DataTable";
import SettingsDashboard from "./SettingsDashboard";
import { useApp } from "../context/AppContext";
import { useTheme } from "../context/ThemeContext";

function MainContent() {
  const { state, dispatch } = useApp();
  const { theme, toggleTheme } = useTheme();

  const handleFilterToggle = () => {
    dispatch({ type: 'TOGGLE_FILTER_PANEL' });
  };

  const handleAddVoter = () => {
    // TODO: Implement add voter functionality
    console.log('Add voter clicked');
  };

  const handleBackToMain = () => {
    dispatch({ type: 'SET_CURRENT_VIEW', payload: 'main' });
  };

  // Don't render main view if we're in reports view
  if (state.currentView === 'reports') {
    return (
      <main className="main-content">
        <div id="reports-view">
          <div className="toolbar">
            <h1>Reports</h1>
            <div className="toolbar-left">
              <button onClick={handleBackToMain} className="btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m15 18-6-6 6-6"/>
                </svg>
                Back
              </button>
            </div>
          </div>
          <div className="reports-content">
            {/* Placeholder for charts */}
            <p>Chart placeholder</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="main-content">
      <div id="main-view">
        <div className="toolbar">
          <div className="toolbar-left">
            <h1>Voter Database</h1>
          </div>
        <div className="toolbar-right">
          <div className="theme-toggle">
            <button onClick={toggleTheme} className="btn" aria-label="Toggle theme">
              {theme === 'light' ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
                  strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 2v2"/>
                  <path d="M13 8.129A4 4 0 0 1 15.873 11"/>
                  <path d="m19 5-1.256 1.256"/>
                  <path d="M20 12h2"/>
                  <path d="M9 8a5 5 0 1 0 7 7 7 7 0 1 1-7-7"/>
                </svg>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
                  strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="5"></circle>
                  <line x1="12" y1="1" x2="12" y2="3"></line>
                  <line x1="12" y1="21" x2="12" y2="23"></line>
                  <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                  <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                  <line x1="1" y1="12" x2="3" y2="12"></line>
                  <line x1="21" y1="12" x2="23" y2="12"></line>
                  <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                  <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
              )}
              {theme === 'light' ? 'Light' : 'Dark'}
            </button>
          </div>
          <button
            onClick={handleFilterToggle}
            className={`btn filter-btn ${state.isFilterPanelOpen ? 'is-active' : ''}`}
            aria-label="Show filter options"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
            Filter
          </button>
          <button onClick={handleAddVoter} className="btn btn-primary" aria-label="Add voter">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add Voter
          </button>
        </div>
      </div>

      {/* Import Progress Indicator */}
      {state.importProgress && (
        <div className="import-progress">
          <div className="import-progress-content">
            <svg className="spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 12a9 9 0 11-6.219-8.56"/>
            </svg>
            <span>{state.importProgress}</span>
          </div>
        </div>
      )}

      <StatsCards />

      <FilterPanel />

      <SettingsDashboard />

      <DataTable />
      </div>
      <div id="reports-view" style={{display: 'none'}}>
        <div className="toolbar">
          <h1>Reports</h1>
          <div className="toolbar-left">
            <button id="back-to-main" className="btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m15 18-6-6 6-6"/>
              </svg>
              Back
            </button>
          </div>
        </div>
        <div className="reports-content">
          {/* Placeholder for charts */}
          <p>Chart placeholder</p>
        </div>
      </div>
    </main>
  );
}

export default MainContent;
