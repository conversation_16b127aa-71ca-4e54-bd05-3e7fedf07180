import React, { useState } from 'react';
import { useApp } from '../context/AppContext';

const CSVImportTest: React.FC = () => {
  const { state, database } = useApp();
  const [importResult, setImportResult] = useState<any>(null);
  const [isImporting, setIsImporting] = useState(false);

  // Log state changes for debugging
  React.useEffect(() => {
    console.log('🔍 Database state:', {
      isInitialized: state.isDatabaseInitialized,
      isLoading: state.isLoading,
      error: state.error,
      votersCount: state.voters.length
    });
  }, [state.isDatabaseInitialized, state.isLoading, state.error, state.voters.length]);

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportResult(null);

    try {
      // Validate CSV first
      const validation = await database.validateCSV(file);
      if (!validation.valid) {
        setImportResult({
          success: false,
          errors: validation.errors
        });
        return;
      }

      // Import CSV
      const result = await database.importCSV(file);
      setImportResult(result);
    } catch (error) {
      setImportResult({
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleClearDatabase = async () => {
    if (window.confirm('Are you sure you want to clear all data?')) {
      try {
        await database.clearDatabase();
        setImportResult(null);
      } catch (error) {
        console.error('Failed to clear database:', error);
      }
    }
  };

  const handleExportCSV = async () => {
    try {
      const csvData = await database.exportCSV();
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'voters_export.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export CSV:', error);
    }
  };

  const handleExportDatabase = () => {
    try {
      const dbData = database.exportDatabase();
      const blob = new Blob([dbData], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'electixir_database.sqlite';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export database:', error);
    }
  };

  const handleTestSampleData = async () => {
    try {
      setIsImporting(true);
      setImportResult(null);

      // Fetch sample CSV file
      const response = await fetch('/sample-voter-data-1.csv');
      const csvText = await response.text();

      // Create a File object from the CSV text
      const file = new File([csvText], 'sample-voter-data-1.csv', { type: 'text/csv' });

      // Validate and import
      const validation = await database.validateCSV(file);
      if (!validation.valid) {
        setImportResult({
          success: false,
          errors: validation.errors
        });
        return;
      }

      const result = await database.importCSV(file);
      setImportResult(result);
    } catch (error) {
      setImportResult({
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleTestHouseholds = async () => {
    try {
      const households = await database.getHouseholds();
      console.log('🏠 Households found:', households);
      alert(`Found ${households.length} households. Check console for details.`);
    } catch (error) {
      console.error('Failed to get households:', error);
      alert('Failed to get households. Check console for details.');
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#171717',
      border: '1px solid #ccc',
      padding: '20px',
      borderRadius: '8px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 1000,
      maxWidth: '400px'
    }}>
      <h3>Database Test Panel</h3>

      <div style={{ marginBottom: '10px' }}>
        <strong>Status:</strong>
        <div>Database Initialized: {state.isDatabaseInitialized ? '✅' : '❌'}</div>
        <div>Loading: {state.isLoading ? '⏳' : '✅'}</div>
        <div>Voters Count: {state.voters.length}</div>
        {state.error && <div style={{ color: 'red' }}>Error: {state.error}</div>}
      </div>

      <div style={{ marginBottom: '10px' }}>
        <label>
          <strong>Import CSV:</strong>
          <input
            type="file"
            accept=".csv"
            onChange={handleFileImport}
            disabled={isImporting || !state.isDatabaseInitialized}
            style={{ display: 'block', marginTop: '5px' }}
          />
        </label>
        {isImporting && <div>Importing...</div>}
      </div>

      {importResult && (
        <div style={{
          marginBottom: '10px',
          padding: '10px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          color: "black",
          backgroundColor: importResult.success ? '#d4edda' : '#f8d7da'
        }}>
          <strong>Import Result:</strong>
          <div>Success: {importResult.success ? '✅' : '❌'}</div>
          {importResult.totalRows && <div>Total Rows: {importResult.totalRows}</div>}
          {importResult.importedRows && <div>New Records: {importResult.importedRows}</div>}
          {importResult.updatedRows && <div>Updated Records: {importResult.updatedRows}</div>}
          {importResult.skippedRows && <div>Skipped: {importResult.skippedRows}</div>}
          {importResult.errors && importResult.errors.length > 0 && (
            <div>
              <strong>Errors:</strong>
              <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                {importResult.errors.slice(0, 5).map((error: string, index: number) => (
                  <li key={index} style={{ fontSize: '12px' }}>{error}</li>
                ))}
                {importResult.errors.length > 5 && (
                  <li style={{ fontSize: '12px' }}>... and {importResult.errors.length - 5} more</li>
                )}
              </ul>
            </div>
          )}
        </div>
      )}

      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        <button
          onClick={handleTestSampleData}
          disabled={isImporting || !state.isDatabaseInitialized}
          style={{ padding: '5px 10px', fontSize: '12px', backgroundColor: '#28a745', color: 'white' }}
        >
          Test Sample Data
        </button>

        <button
          onClick={handleTestHouseholds}
          disabled={!state.isDatabaseInitialized || state.voters.length === 0}
          style={{ padding: '5px 10px', fontSize: '12px', backgroundColor: '#17a2b8', color: 'white' }}
        >
          Test Households
        </button>

        <button
          onClick={handleExportCSV}
          disabled={!state.isDatabaseInitialized || state.voters.length === 0}
          style={{ padding: '5px 10px', fontSize: '12px' }}
        >
          Export CSV
        </button>

        <button
          onClick={handleExportDatabase}
          disabled={!state.isDatabaseInitialized}
          style={{ padding: '5px 10px', fontSize: '12px' }}
        >
          Export DB
        </button>

        <button
          onClick={handleClearDatabase}
          disabled={!state.isDatabaseInitialized}
          style={{ padding: '5px 10px', fontSize: '12px', backgroundColor: '#dc3545', color: 'white' }}
        >
          Clear DB
        </button>
      </div>

      <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        <strong>Sample Files:</strong>
        <div>• /src-html/sample-voter-data-1.csv</div>
        <div>• /src-html/sample-voter-data-2.csv</div>
      </div>
    </div>
  );
};

export default CSVImportTest;
