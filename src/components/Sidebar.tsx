import { useState } from "react";
import { useApp } from "../context/AppContext";
import { useClickOutside } from "../hooks/useClickOutside";
import { usePollingStations } from "../hooks/usePollingStations";
import PollingStationList from "./PollingStationList";
import SidebarFooter from "./SidebarFooter";

function Sidebar() {
  const { state, dispatch, database } = useApp();
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  const {
    pollingStations,
    toggleStationOpen,
    toggleStationSelected,
    toggleSectionSelected,
    getSelectedCount,
    getSelectedSectionsCount,
    totalStations
  } = usePollingStations();

  const dropdownRef = useClickOutside<HTMLDivElement>(() => {
    if (hasInteracted) {
      setIsSettingsDropdownOpen(false);
    }
  });

  const handleReportsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    dispatch({ type: 'SET_CURRENT_VIEW', payload: 'reports' });
  };

  const handleSettingsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setHasInteracted(true);
    setIsSettingsDropdownOpen(!isSettingsDropdownOpen);
  };

  const handleManageDataClick = () => {
    dispatch({ type: 'TOGGLE_SETTINGS_DASHBOARD' });
    setIsSettingsDropdownOpen(false);
  };

  const handleImportCSVClick = () => {
    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.multiple = true; // Allow multiple file selection
    input.onchange = async (e) => {
      const files = Array.from((e.target as HTMLInputElement).files || []);
      if (files.length > 0) {
        try {
          let totalImported = 0;
          let totalUpdated = 0;
          let totalSkipped = 0;
          let totalErrors: string[] = [];

          // Show progress
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: `Processing ${files.length} file(s)...` });

          for (let i = 0; i < files.length; i++) {
            const file = files[i];
            dispatch({ type: 'SET_IMPORT_PROGRESS', payload: `Processing ${file.name} (${i + 1}/${files.length})...` });
            console.log(`📁 Processing file ${i + 1}/${files.length}: ${file.name}`);

            try {
              const result = await database.importCSV(file);
              totalImported += result.importedRows;
              totalUpdated += result.updatedRows || 0;
              totalSkipped += result.skippedRows;
              totalErrors.push(...result.errors);
            } catch (error) {
              totalErrors.push(`${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }

          // Clear progress
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });

          const summary = `Import completed!\nFiles processed: ${files.length}\nNew records: ${totalImported}\nUpdated records: ${totalUpdated}\nSkipped: ${totalSkipped}${totalErrors.length > 0 ? `\nErrors: ${totalErrors.length}` : ''}`;
          alert(summary);

          if (totalErrors.length > 0) {
            console.error('Import errors:', totalErrors);
          }
        } catch (error) {
          alert(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };
    input.click();
    setIsSettingsDropdownOpen(false);
  };

  const handleExportCSVClick = async () => {
    try {
      const csvData = await database.exportCSV();
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `voters_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setIsSettingsDropdownOpen(false);
  };

  const handleExportPDFClick = () => {
    // TODO: Implement PDF export
    alert('PDF export feature coming soon!');
    setIsSettingsDropdownOpen(false);
  };

  const handleBackupDBClick = () => {
    try {
      const dbData = database.exportDatabase();
      const blob = new Blob([dbData], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `electixir_backup_${new Date().toISOString().split('T')[0]}.sqlite`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      alert(`Backup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setIsSettingsDropdownOpen(false);
  };

  return (
    <aside className="sidebar" aria-label="Main navigation">
      <header className="sidebar-header">
        <div className="sidebar-title">Polling Stations</div>
      </header>
      <nav className="sidebar-content">
        <PollingStationList
          pollingStations={pollingStations}
          onToggle={(e, stationId) => {
            e.preventDefault();
            toggleStationOpen(stationId);
          }}
          onStationSelect={toggleStationSelected}
          onSectionSelect={toggleSectionSelected}
        />
      </nav>
      <SidebarFooter
        totalStations={totalStations}
        selectedStations={getSelectedCount()}
        selectedSections={getSelectedSectionsCount()}
        onReportsClick={handleReportsClick}
        onSettingsClick={handleSettingsClick}
        onManageDataClick={handleManageDataClick}
        onImportCSVClick={handleImportCSVClick}
        onExportCSVClick={handleExportCSVClick}
        onExportPDFClick={handleExportPDFClick}
        onBackupDBClick={handleBackupDBClick}
        isSettingsOpen={isSettingsDropdownOpen}
        dropdownRef={dropdownRef}
      />
    </aside>
  );
}

export default Sidebar;
