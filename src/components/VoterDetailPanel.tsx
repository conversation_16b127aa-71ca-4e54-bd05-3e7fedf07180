import { useState, useEffect } from "react";
import { useApp } from "../context/AppContext";
import { useEscapeKey } from "../hooks/useClickOutside";

function VoterDetailPanel() {
  const { state, dispatch } = useApp();
  const { isVoterPanelOpen, selectedVoter } = state;
  const [isEditing, setIsEditing] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Close panel on escape key
  useEscapeKey(() => {
    if (isVoterPanelOpen) {
      handleClose();
    }
  });

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_VOTER_PANEL' });
    setIsEditing(false);
  };

  // Force re-render when panel opens to ensure details elements work properly
  useEffect(() => {
    if (isVoterPanelOpen && selectedVoter) {
      setForceUpdate(prev => prev + 1);
    }
  }, [isVoterPanelOpen, selectedVoter]);



  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    // TODO: Implement save functionality
    setIsEditing(false);
    console.log('Save voter changes');
  };

  const handleAddNote = () => {
    // TODO: Implement add note functionality
    console.log('Add note');
  };

  // Always render for smooth animations - don't conditionally render
  if (!selectedVoter) {
    return (
      <div className="voter-detail-panel">
        <div className="panel-header">
          <h2 className="panel-title">Voter Details</h2>
          <div className="panel-actions">
            <button onClick={handleClose} className="panel-close" aria-label="Close panel">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
                strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
        <div className="panel-content">
          <p>No voter selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`voter-detail-panel ${isVoterPanelOpen ? 'is-open' : ''}`}>
      <div className="panel-header">
        <h2 className="panel-title">Voter Details</h2>
        <div className="panel-actions">
          <button
            onClick={handleEdit}
            className="btn btn-edit"
            style={{ display: isEditing ? 'none' : 'inline-flex' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            Edit
          </button>
          <button
            onClick={handleSave}
            className="btn btn-primary btn-save"
            style={{ display: isEditing ? 'inline-flex' : 'none' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17 21 17 13 7 13 7 21"></polyline>
              <polyline points="7 3 7 8 15 8"></polyline>
            </svg>
            Save
          </button>
          <button onClick={handleClose} className="panel-close" aria-label="Close panel">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <div className="panel-content" key={`panel-${selectedVoter?.id}-${forceUpdate}`}>
        {/* Voter Profile Section */}
        <section className="panel-section">
          <details open>
            <summary>
              <h3 className="section-title">Voter Profile</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            <div className="section-grid">
              <div className="detail-item">
                <span className="detail-label">Voter's Name</span>
                <span className="detail-value">{selectedVoter.name}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">EPIC Number</span>
                <span className="detail-value">{selectedVoter.epic}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Father's Name</span>
                <span className="detail-value">{selectedVoter.fatherName || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">House Number</span>
                <span className="detail-value">{selectedVoter.houseNumber || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Age</span>
                <span className="detail-value">{selectedVoter.age}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Gender</span>
                <span className="detail-value">{selectedVoter.gender}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Polling Station</span>
                <span className="detail-value">{selectedVoter.pollingStation}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Section</span>
                <span className="detail-value">{selectedVoter.section || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Voter Status</span>
                <span className="detail-value">{selectedVoter.status}</span>
              </div>
            </div>
          </details>
        </section>

        {/* Combined Section for Status, Demographics and Notes */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Additional Information</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            {/* Status Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Voter Affiliation</h4>
              <div className="status-grid">
                <div className="status-item">
                  <span className="status-indicator"></span>
                  <span className="status-label">Support Status</span>
                  <span className="status-value">Unknown</span>
                </div>
              </div>
            </div>

            {/* Demographics Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Demographics</h4>
              <div className="section-grid">
                <div className="detail-item">
                  <span className="detail-label">Education</span>
                  <span className="detail-value">{selectedVoter.education || '-'}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Qualification</span>
                  <span className="detail-value">{selectedVoter.qualification || '-'}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Community</span>
                  <span className="detail-value">{selectedVoter.community}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Religion</span>
                  <span className="detail-value">{selectedVoter.religion}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Economic Status</span>
                  <span className="detail-value">{selectedVoter.economicStatus}</span>
                </div>
              </div>
            </div>

            {/* Notes Section */}
            <div className="panel-subsection">
              <div className="section-header">
                <h4 className="subsection-title">Notes</h4>
                <button onClick={handleAddNote} className="btn btn-small">Add Note</button>
              </div>
              <div className="notes-content">
                <p className="note-text">Regular voter, attends all meetings. Prefers evening visits.</p>
              </div>
            </div>
          </details>
        </section>

        {/* Contact Details Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Contact Details</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            <div className="section-grid">
              <div className="detail-item">
                <span className="detail-label">Phone</span>
                <span className="detail-value">{selectedVoter.phone || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Email</span>
                <span className="detail-value">{selectedVoter.email || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Facebook</span>
                <span className="detail-value">{selectedVoter.facebook || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Instagram</span>
                <span className="detail-value">{selectedVoter.instagram || '-'}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">Twitter</span>
                <span className="detail-value">{selectedVoter.twitter || '-'}</span>
              </div>
            </div>
          </details>
        </section>

        {/* Family Members Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Family Members</h3>
              <div className="summary-right">
                <span className="badge badge-count">2 members</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </div>
            </summary>
            <div className="family-members">
              <div className="family-member">
                <span className="member-name">Ramesh Kumar</span>
                <span className="member-relation">Father</span>
              </div>
              <div className="family-member">
                <span className="member-name">Sita Devi</span>
                <span className="member-relation">Mother</span>
              </div>
            </div>
          </details>
        </section>

        {/* Transactions Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Transactions</h3>
              <div className="summary-right">
                <span className="badge badge-amount">₹7,000</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </div>
            </summary>
            <div className="transactions-table">
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Purpose</th>
                    <th>Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>15/07/2025</td>
                    <td>Medical Assistance</td>
                    <td>₹5,000</td>
                  </tr>
                  <tr>
                    <td>02/05/2025</td>
                    <td>Festival Support</td>
                    <td>₹2,000</td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan={2}>Total</td>
                    <td>₹7,000</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </details>
        </section>
      </div>
    </div>
  );
}

export default VoterDetailPanel;
