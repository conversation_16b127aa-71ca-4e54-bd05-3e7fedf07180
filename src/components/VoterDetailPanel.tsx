import { useState, useEffect } from "react";
import { useApp } from "../context/AppContext";
import { useEscapeKey } from "../hooks/useClickOutside";
import { useDatabase } from "../hooks/useDatabase";
import SettingsService from "../database/SettingsService";

function VoterDetailPanel() {
  const { state, dispatch } = useApp();
  const { isVoterPanelOpen, selectedVoter } = state;
  const [isEditing, setIsEditing] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);
  const [formData, setFormData] = useState<any>({});
  const [categoryOptions, setCategoryOptions] = useState<{
    community: string[];
    religion: string[];
    economic_status: string[];
    education: string[];
    occupation: string[];
  }>({
    community: [],
    religion: [],
    economic_status: [],
    education: [],
    occupation: []
  });

  const { voterService } = useDatabase();

  // Close panel on escape key
  useEscapeKey(() => {
    if (isVoterPanelOpen) {
      handleClose();
    }
  });

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_VOTER_PANEL' });
    setIsEditing(false);
  };

  // Initialize form data when voter changes
  useEffect(() => {
    if (selectedVoter) {
      setFormData({
        name: selectedVoter.name || '',
        epic: selectedVoter.epic || '',
        fatherName: selectedVoter.fatherName || '',
        houseNumber: selectedVoter.houseNumber || '',
        age: selectedVoter.age || '',
        gender: selectedVoter.gender || '',
        pollingStation: selectedVoter.pollingStation || '',
        section: selectedVoter.section || '',
        status: selectedVoter.status || '',
        supporter_status: selectedVoter.supporter_status || '',
        education: selectedVoter.education || '',
        qualification: selectedVoter.qualification || '',
        community: selectedVoter.community || '',
        religion: selectedVoter.religion || '',
        economicStatus: selectedVoter.economicStatus || '',
        phone: selectedVoter.phone || '',
        email: selectedVoter.email || '',
        facebook: selectedVoter.facebook || '',
        instagram: selectedVoter.instagram || '',
        twitter: selectedVoter.twitter || '',
        custom_notes: selectedVoter.custom_notes || ''
      });
    }
  }, [selectedVoter]);

  // Load category options
  useEffect(() => {
    const loadCategoryOptions = async () => {
      const settingsService = new SettingsService();
      try {
        const [community, religion, economic_status, education, occupation] = await Promise.all([
          settingsService.getCategorySettings('community'),
          settingsService.getCategorySettings('religion'),
          settingsService.getCategorySettings('economic_status'),
          settingsService.getCategorySettings('education'),
          settingsService.getCategorySettings('occupation')
        ]);

        setCategoryOptions({
          community,
          religion,
          economic_status,
          education,
          occupation
        });
      } catch (error) {
        console.error('Failed to load category options:', error);
      }
    };

    if (isVoterPanelOpen) {
      loadCategoryOptions();
    }
  }, [isVoterPanelOpen]);

  // Force re-render when panel opens to ensure details elements work properly
  useEffect(() => {
    if (isVoterPanelOpen && selectedVoter) {
      setForceUpdate(prev => prev + 1);
    }
  }, [isVoterPanelOpen, selectedVoter]);



  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!selectedVoter || !voterService) return;

    try {
      // Prepare voter data for update
      const updatedVoterData = {
        ...formData,
        id: selectedVoter.id,
        epic_number: formData.epic,
        relationship_name: formData.fatherName,
        house_number: formData.houseNumber,
        birth_year: formData.age ? new Date().getFullYear() - parseInt(formData.age) : undefined,
        polling_station: formData.pollingStation,
        economic_status: formData.economicStatus
      };

      await voterService.updateVoter(selectedVoter.id, updatedVoterData);

      // Update the selected voter in the context
      dispatch({
        type: 'TOGGLE_VOTER_PANEL',
        payload: { ...selectedVoter, ...updatedVoterData }
      });

      setIsEditing(false);
      console.log('✅ Voter updated successfully');
    } catch (error) {
      console.error('Failed to save voter changes:', error);
      alert('Failed to save changes. Please try again.');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddNote = () => {
    // TODO: Implement add note functionality
    console.log('Add note');
  };

  // Helper component for editable fields
  const EditableField = ({
    label,
    field,
    type = 'text',
    options = null
  }: {
    label: string;
    field: string;
    type?: 'text' | 'select' | 'textarea';
    options?: string[] | null;
  }) => {
    const value = formData[field] || '';

    if (!isEditing) {
      return (
        <div className="detail-item">
          <span className="detail-label">{label}</span>
          <span className="detail-value">{value || '-'}</span>
        </div>
      );
    }

    if (type === 'select' && options) {
      return (
        <div className="detail-item">
          <span className="detail-label">{label}</span>
          <select
            className="detail-value editable"
            value={value}
            onChange={(e) => handleInputChange(field, e.target.value)}
          >
            <option value="">Select {label}</option>
            {options.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
      );
    }

    if (type === 'textarea') {
      return (
        <div className="detail-item">
          <span className="detail-label">{label}</span>
          <textarea
            className="detail-value editable"
            value={value}
            onChange={(e) => handleInputChange(field, e.target.value)}
            rows={3}
            placeholder={`Enter ${label.toLowerCase()}`}
          />
        </div>
      );
    }

    return (
      <div className="detail-item">
        <span className="detail-label">{label}</span>
        <input
          type="text"
          className="detail-value editable"
          value={value}
          onChange={(e) => handleInputChange(field, e.target.value)}
          placeholder={`Enter ${label.toLowerCase()}`}
        />
      </div>
    );
  };

  // Always render for smooth animations - don't conditionally render
  if (!selectedVoter) {
    return (
      <div className="voter-detail-panel">
        <div className="panel-header">
          <h2 className="panel-title">Voter Details</h2>
          <div className="panel-actions">
            <button onClick={handleClose} className="panel-close" aria-label="Close panel">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
                strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
        <div className="panel-content">
          <p>No voter selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`voter-detail-panel ${isVoterPanelOpen ? 'is-open' : ''}`}>
      <div className="panel-header">
        <h2 className="panel-title">Voter Details</h2>
        <div className="panel-actions">
          <button
            onClick={handleEdit}
            className="btn btn-edit"
            style={{ display: isEditing ? 'none' : 'inline-flex' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            Edit
          </button>
          <button
            onClick={handleSave}
            className="btn btn-primary btn-save"
            style={{ display: isEditing ? 'inline-flex' : 'none' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17 21 17 13 7 13 7 21"></polyline>
              <polyline points="7 3 7 8 15 8"></polyline>
            </svg>
            Save
          </button>
          <button onClick={handleClose} className="panel-close" aria-label="Close panel">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <div className="panel-content" key={`panel-${selectedVoter?.id}-${forceUpdate}`}>
        {/* Voter Profile Section */}
        <section className="panel-section">
          <details open>
            <summary>
              <h3 className="section-title">Voter Profile</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            <div className="section-grid">
              <EditableField label="Voter's Name" field="name" />
              <EditableField label="EPIC Number" field="epic" />
              <EditableField label="Father's Name" field="fatherName" />
              <EditableField label="House Number" field="houseNumber" />
              <EditableField label="Age" field="age" />
              <EditableField
                label="Gender"
                field="gender"
                type="select"
                options={['Male', 'Female', 'Other']}
              />
              <EditableField label="Polling Station" field="pollingStation" />
              <EditableField label="Section" field="section" />
              <EditableField
                label="Voter Status"
                field="status"
                type="select"
                options={['Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified']}
              />
            </div>
          </details>
        </section>

        {/* Combined Section for Status, Demographics and Notes */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Additional Information</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            {/* Status Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Voter Affiliation</h4>
              <div className="section-grid">
                <EditableField
                  label="Support Status"
                  field="supporter_status"
                  type="select"
                  options={['Strong Supporter', 'Potential Supporter', 'Undecided', 'Opposed']}
                />
              </div>
            </div>

            {/* Demographics Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Demographics</h4>
              <div className="section-grid">
                <EditableField
                  label="Education"
                  field="education"
                  type="select"
                  options={categoryOptions.education}
                />
                <EditableField
                  label="Qualification"
                  field="qualification"
                  type="select"
                  options={categoryOptions.occupation}
                />
                <EditableField
                  label="Community"
                  field="community"
                  type="select"
                  options={categoryOptions.community}
                />
                <EditableField
                  label="Religion"
                  field="religion"
                  type="select"
                  options={categoryOptions.religion}
                />
                <EditableField
                  label="Economic Status"
                  field="economicStatus"
                  type="select"
                  options={categoryOptions.economic_status}
                />
              </div>
            </div>

            {/* Notes Section */}
            <div className="panel-subsection">
              <div className="section-header">
                <h4 className="subsection-title">Notes</h4>
                {!isEditing && <button onClick={handleAddNote} className="btn btn-small">Add Note</button>}
              </div>
              <div className="notes-content">
                <EditableField
                  label="Notes"
                  field="custom_notes"
                  type="textarea"
                />
              </div>
            </div>
          </details>
        </section>

        {/* Contact Details Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Contact Details</h3>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            <div className="section-grid">
              <EditableField label="Phone" field="phone" />
              <EditableField label="Email" field="email" />
              <EditableField label="Facebook" field="facebook" />
              <EditableField label="Instagram" field="instagram" />
              <EditableField label="Twitter" field="twitter" />
            </div>
          </details>
        </section>

        {/* Family Members Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Family Members</h3>
              <div className="summary-right">
                <span className="badge badge-count">2 members</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </div>
            </summary>
            <div className="family-members">
              <div className="family-member">
                <span className="member-name">Ramesh Kumar</span>
                <span className="member-relation">Father</span>
              </div>
              <div className="family-member">
                <span className="member-name">Sita Devi</span>
                <span className="member-relation">Mother</span>
              </div>
            </div>
          </details>
        </section>

        {/* Transactions Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Transactions</h3>
              <div className="summary-right">
                <span className="badge badge-amount">₹7,000</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </div>
            </summary>
            <div className="transactions-table">
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Purpose</th>
                    <th>Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>15/07/2025</td>
                    <td>Medical Assistance</td>
                    <td>₹5,000</td>
                  </tr>
                  <tr>
                    <td>02/05/2025</td>
                    <td>Festival Support</td>
                    <td>₹2,000</td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan={2}>Total</td>
                    <td>₹7,000</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </details>
        </section>
      </div>
    </div>
  );
}

export default VoterDetailPanel;
