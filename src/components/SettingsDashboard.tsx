import { useState, useRef, useEffect } from "react";
import { useApp, CategoryData } from "../context/AppContext";
import { useLightSweep } from "../hooks/useLightSweep";

function SettingsDashboard() {
  const { state, dispatch } = useApp();
  const { isSettingsDashboardOpen, categoryData } = state;
  const containerRef = useRef<HTMLDivElement>(null);
  const triggerLightSweep = useLightSweep();
  const [selectedCategory, setSelectedCategory] = useState<keyof CategoryData>('community');
  const [newItemInput, setNewItemInput] = useState('');

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_SETTINGS_DASHBOARD' });
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value as keyof CategoryData);
  };

  const handleAddItem = () => {
    const trimmedItem = newItemInput.trim();
    if (trimmedItem) {
      dispatch({
        type: 'ADD_CATEGORY_ITEM',
        payload: { category: selectedCategory, item: trimmedItem }
      });
      setNewItemInput('');
    }
  };

  const handleRemoveItem = (item: string) => {
    dispatch({
      type: 'REMOVE_CATEGORY_ITEM',
      payload: { category: selectedCategory, item }
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddItem();
    }
  };

  const formatCategoryName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ');
  };

  // Trigger light sweep when panel opens
  useEffect(() => {
    if (isSettingsDashboardOpen) {
      triggerLightSweep(containerRef.current);
    }
  }, [isSettingsDashboardOpen, triggerLightSweep]);

  return (
    <div ref={containerRef} className={`settings-dashboard-container ${isSettingsDashboardOpen ? 'is-open' : ''}`}>
      <div className="settings-dashboard">
        <div className="filter-header">
          <div className="filter-title">
            <svg className="dropdown-item-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M4 7V4h16v3"/>
              <path d="M9 20h6"/>
              <path d="M12 4v16"/>
            </svg>
            Manage Data
          </div>
          <button onClick={handleClose} className="panel-close" aria-label="Close panel">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"
              strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div className="settings-controls">
          <div className="filter-group">
            <label htmlFor="category-select">Category</label>
            <div className="select-wrapper">
              <select
                id="category-select"
                value={selectedCategory}
                onChange={handleCategoryChange}
              >
                <option value="community">Community</option>
                <option value="religion">Religion</option>
                <option value="economic_status">Economic Status</option>
              </select>
            </div>
          </div>
          <div className="add-item-form">
            <input
              type="text"
              id="new-item-input"
              placeholder="Enter new item"
              value={newItemInput}
              onChange={(e) => setNewItemInput(e.target.value)}
              onKeyPress={handleKeyPress}
            />
            <button onClick={handleAddItem} className="btn btn-primary">Add</button>
          </div>
        </div>
        <div className="data-list">
          <h3>{formatCategoryName(selectedCategory)}</h3>
          <ul id="item-list">
            {categoryData[selectedCategory].map((item, index) => (
              <li key={index}>
                <span>{item}</span>
                <div className="item-actions">
                  <button className="edit-btn">Edit</button>
                  <button
                    onClick={() => handleRemoveItem(item)}
                    className="delete-btn"
                  >
                    Delete
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

export default SettingsDashboard;
