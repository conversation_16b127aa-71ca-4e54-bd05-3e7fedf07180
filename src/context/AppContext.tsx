import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { useDatabase } from '../hooks/useDatabase';

// Types
export interface Voter {
  id: number;
  name: string;
  relationship_type?: 'Father' | 'Mother' | 'Husband' | 'Others';
  relationship_name?: string;
  gender: 'Male' | 'Female' | 'Other';
  birth_year?: number;
  epic_number: string;
  house_number?: string;
  polling_station?: string;
  section?: string;

  // Extended contact information
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;

  // Voter status
  status?: 'Active' | 'Expired' | 'Shifted' | 'Duplicate' | 'Missing' | 'Disqualified';

  // Political information
  supporter_status?: 'Strong Supporter' | 'Potential Supporter' | 'Undecided' | 'Opposed';

  // Demographics
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  custom_notes?: string;

  // Computed fields for backward compatibility
  age?: number; // Computed from birth_year
  epic?: string; // Alias for epic_number
  pollingStation?: string; // Alias for polling_station
  economicStatus?: string; // Alias for economic_status
  fatherName?: string; // Alias for relationship_name when relationship_type is 'Father'
  qualification?: string; // Alias for education
}

export interface Filters {
  gender: string;
  ageFrom: number;
  ageTo: number;
  community: string;
  religion: string;
  economicStatus: string; // Keep for backward compatibility
  economic_status: string; // New field name
  searchTerm: string;
  status: string;
  polling_station: string;
  section: string;
  supporter_status: string;
  // Selected polling stations and sections for filtering
  selectedPollingStations: string[];
  selectedSections: string[];
}

export interface CategoryData {
  community: string[];
  religion: string[];
  economic_status: string[];
}

export interface AppState {
  // UI State
  isFilterPanelOpen: boolean;
  isSettingsDashboardOpen: boolean;
  isVoterPanelOpen: boolean;
  activeDropdown: string | null;
  currentView: 'main' | 'reports';
  selectedVoter: Voter | null;

  // Data State
  voters: Voter[];
  filters: Filters;
  categoryData: CategoryData;

  // Database State
  isDatabaseInitialized: boolean;

  // Loading State
  isLoading: boolean;
  error: string | null;
  importProgress: string | null;
}

// Action Types
type AppAction =
  | { type: 'TOGGLE_FILTER_PANEL' }
  | { type: 'TOGGLE_SETTINGS_DASHBOARD' }
  | { type: 'TOGGLE_VOTER_PANEL'; payload?: Voter }
  | { type: 'SET_ACTIVE_DROPDOWN'; payload: string | null }
  | { type: 'SET_CURRENT_VIEW'; payload: 'main' | 'reports' }
  | { type: 'UPDATE_FILTER'; payload: { key: keyof Filters; value: any } }
  | { type: 'CLEAR_FILTERS' }
  | { type: 'ADD_CATEGORY_ITEM'; payload: { category: keyof CategoryData; item: string } }
  | { type: 'REMOVE_CATEGORY_ITEM'; payload: { category: keyof CategoryData; item: string } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_VOTERS'; payload: Voter[] }
  | { type: 'SET_DATABASE_INITIALIZED'; payload: boolean }
  | { type: 'UPDATE_SELECTED_POLLING_STATIONS'; payload: string[] }
  | { type: 'UPDATE_SELECTED_SECTIONS'; payload: string[] }
  | { type: 'SET_IMPORT_PROGRESS'; payload: string | null };

// Initial State
const initialFilters: Filters = {
  gender: 'All',
  ageFrom: 18,
  ageTo: 120,
  community: 'All',
  religion: 'All',
  economicStatus: 'All', // Backward compatibility
  economic_status: 'All',
  searchTerm: '',
  status: 'All',
  polling_station: 'All',
  section: 'All',
  supporter_status: 'All',
  selectedPollingStations: [],
  selectedSections: []
};

const initialCategoryData: CategoryData = {
  community: ['General', 'OBC', 'SC', 'ST'],
  religion: ['Christian', 'Hindu', 'Muslim', 'Buddhist'],
  economic_status: ['APL', 'BPL', 'Middle Income', 'High Income']
};

const initialVoters: Voter[] = [];

const initialState: AppState = {
  isFilterPanelOpen: false,
  isSettingsDashboardOpen: false,
  isVoterPanelOpen: false,
  activeDropdown: null,
  currentView: 'main',
  selectedVoter: null,
  voters: initialVoters,
  filters: initialFilters,
  categoryData: initialCategoryData,
  isDatabaseInitialized: false,
  isLoading: false,
  error: null,
  importProgress: null
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'TOGGLE_FILTER_PANEL':
      return {
        ...state,
        isFilterPanelOpen: !state.isFilterPanelOpen,
        activeDropdown: null // Close any open dropdowns
      };

    case 'TOGGLE_SETTINGS_DASHBOARD':
      return {
        ...state,
        isSettingsDashboardOpen: !state.isSettingsDashboardOpen,
        activeDropdown: null // Close any open dropdowns
      };

    case 'TOGGLE_VOTER_PANEL':
      return {
        ...state,
        isVoterPanelOpen: !state.isVoterPanelOpen,
        selectedVoter: action.payload || state.selectedVoter
      };

    case 'SET_ACTIVE_DROPDOWN':
      return {
        ...state,
        activeDropdown: action.payload
      };

    case 'SET_CURRENT_VIEW':
      return {
        ...state,
        currentView: action.payload
      };

    case 'UPDATE_FILTER':
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.payload.key]: action.payload.value
        }
      };

    case 'CLEAR_FILTERS':
      return {
        ...state,
        filters: initialFilters
      };

    case 'ADD_CATEGORY_ITEM':
      const { category, item } = action.payload;
      if (state.categoryData[category].includes(item)) {
        return state; // Item already exists
      }
      return {
        ...state,
        categoryData: {
          ...state.categoryData,
          [category]: [...state.categoryData[category], item]
        }
      };

    case 'REMOVE_CATEGORY_ITEM':
      return {
        ...state,
        categoryData: {
          ...state.categoryData,
          [action.payload.category]: state.categoryData[action.payload.category].filter(
            item => item !== action.payload.item
          )
        }
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      };

    case 'SET_VOTERS':
      return {
        ...state,
        voters: action.payload
      };

    case 'SET_DATABASE_INITIALIZED':
      return {
        ...state,
        isDatabaseInitialized: action.payload
      };

    case 'UPDATE_SELECTED_POLLING_STATIONS':
      return {
        ...state,
        filters: {
          ...state.filters,
          selectedPollingStations: action.payload
        }
      };

    case 'UPDATE_SELECTED_SECTIONS':
      return {
        ...state,
        filters: {
          ...state.filters,
          selectedSections: action.payload
        }
      };

    case 'SET_IMPORT_PROGRESS':
      return {
        ...state,
        importProgress: action.payload
      };

    default:
      return state;
  }
}

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // Database operations
  database: {
    loadVoters: (filters?: any) => Promise<void>;
    getHouseholds: (filters?: any) => Promise<any[]>;
    addVoter: (voter: any) => Promise<number>;
    updateVoter: (id: number, voter: any) => Promise<void>;
    deleteVoter: (id: number) => Promise<void>;
    importCSV: (file: File) => Promise<any>;
    exportCSV: (filters?: any) => Promise<string>;
    validateCSV: (file: File) => Promise<{ valid: boolean; errors: string[] }>;
    exportDatabase: () => Uint8Array;
    importDatabase: (data: Uint8Array) => Promise<void>;
    clearDatabase: () => Promise<void>;
  };
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const database = useDatabase();

  // Sync database state with app state
  useEffect(() => {
    dispatch({ type: 'SET_DATABASE_INITIALIZED', payload: database.isInitialized });
    dispatch({ type: 'SET_LOADING', payload: database.isLoading });
    dispatch({ type: 'SET_ERROR', payload: database.error });

    // Force reload voters when database becomes initialized
    if (database.isInitialized && !database.isLoading) {
      console.log('🔄 Database initialized, forcing voter reload...');
      database.loadVoters();
    }
  }, [database.isInitialized, database.isLoading, database.error, database.loadVoters]);

  // Sync voters from database
  useEffect(() => {
    console.log(`🔄 Database voters changed: ${database.voters.length} voters`);

    // Always sync, even if empty (to clear UI when database is cleared)
    const appVoters: Voter[] = database.voters
      .filter(voter => voter.id !== undefined) // Filter out voters without IDs
      .map(voter => ({
        ...voter,
        id: voter.id!, // We know it's defined due to filter
        // Add backward compatibility fields
        age: voter.birth_year ? new Date().getFullYear() - voter.birth_year : undefined,
        epic: voter.epic_number,
        pollingStation: voter.polling_station,
        economicStatus: voter.economic_status,
        fatherName: voter.relationship_type === 'Father' ? voter.relationship_name : undefined,
        qualification: voter.education
      }));

    dispatch({ type: 'SET_VOTERS', payload: appVoters });
    console.log(`✅ Updated app state with ${appVoters.length} voters`);
  }, [database.voters]);

  const contextValue: AppContextType = {
    state,
    dispatch,
    database: {
      loadVoters: database.loadVoters,
      getHouseholds: database.getHouseholds,
      addVoter: database.addVoter,
      updateVoter: database.updateVoter,
      deleteVoter: database.deleteVoter,
      importCSV: database.importCSV,
      exportCSV: database.exportCSV,
      validateCSV: database.validateCSV,
      exportDatabase: database.exportDatabase,
      importDatabase: database.importDatabase,
      clearDatabase: database.clearDatabase
    }
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Hook
export function useApp(): AppContextType {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
