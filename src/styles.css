/* Optimized CSS for Voter Management Dashboard
 * This CSS targets Mac (WebKit/Safari) and Windows (Chromium/Edge via WebView2)
 * for use with Tauri desktop applications.
 */

/* ===== CSS VARIABLES ===== */
:root {
  color-scheme: light dark;

  /* Core Colors */
  --color-primary: #1a73e8;
  --color-success: #34c759;
  --color-danger: #ff3b30;
  --color-warning: #ff9500;
  --color-info: #8a4dff;

  /* Spacing Scale */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;

  /* Background colors */
  --bg-primary: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  --bg-secondary: rgba(255, 255, 255, 0.25);
  --bg-tertiary: rgba(255, 255, 255, 0.4);
  --bg-card: rgba(255, 255, 255, 0.2);
  --bg-overlay: rgba(249, 250, 250, 0.85);
  --bg-input: rgba(255, 255, 255, 0.3);
  --bg-button: rgba(255, 255, 255, 0.25);
  --bg-button-primary: rgba(26, 115, 232, 0.85);
  --bg-button-hover: rgba(255, 255, 255, 0.35);
  --bg-row-hover: rgba(255, 255, 255, 0.2);
  --bg-selection: rgba(26, 115, 232, 0.15);

  /* Icon background colors */
  --bg-icon-blue: rgba(26, 115, 232, 0.15);
  --bg-icon-green: rgba(138, 77, 255, 0.15);
  --bg-icon-orange: rgba(255, 149, 0, 0.15);
  --bg-icon-red: rgba(255, 59, 48, 0.15);
  --bg-gender-male: var(--bg-icon-blue);
  --bg-gender-female: var(--bg-icon-green);

  /* Text colors */
  --text-primary: #1f1f1f;
  --text-secondary: #333333;
  --text-tertiary: rgba(100, 100, 110, 0.85);
  --text-button: #1f1f1f;
  --text-button-primary: #ffffff;
  --text-positive: #34c759;
  --text-negative: #ff3b30;
  --text-selection: #1a73e8;

  /* Icon text colors */
  --text-icon-blue: var(--color-primary);
  --text-icon-green: var(--color-info);
  --text-icon-orange: var(--color-warning);
  --text-icon-red: var(--color-danger);
  --text-gender-male: var(--color-primary);
  --text-gender-female: var(--color-info);

  /* Border colors */
  --border-primary: rgba(0, 0, 0, 0.15);
  --border-secondary: rgba(0, 0, 0, 0.2);
  --border-active: rgba(26, 115, 232, 0.5);
  --border-focus: rgba(26, 115, 232, 0.7);

  /* Shadow effects */
  --shadow-primary: 0 8px 32px rgba(0, 0, 0, 0.08);
  --shadow-secondary: 0 12px 40px rgba(0, 0, 0, 0.18);
  --shadow-button-primary: 0 4px 16px rgba(26, 115, 232, 0.25);
  --shadow-icon: 0 4px 16px rgba(0, 0, 0, 0.1);

  /* Border radius */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;

  /* Blur effects */
  --blur-light: blur(8px);
  --blur-medium: blur(16px);
  --blur-heavy: blur(24px);

  /* Transitions and animations */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --duration: 0.25s;
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Status colors */
  --status-active: var(--color-success);
  --status-inactive: var(--color-danger);

  /* App region (for Tauri) */
  --app-region-drag: drag;
  --app-region-no-drag: no-drag;
  --webkit-appearance: none;
  --scrollbar-width: 12px;
}

/* ===== DARK THEME VARIABLES ===== */
[data-theme=dark] {
  --bg-primary: linear-gradient(135deg, #212121 0%, #1f1f1f 100%);
  --bg-secondary: rgba(25, 25, 25, 0.3);
  --bg-tertiary: rgba(40, 40, 40, 0.4);
  --bg-card: rgba(25, 25, 25, 0.25);
  --bg-overlay: rgba(32, 32, 32, 0.95);
  --bg-input: rgba(20, 20, 20, 0.35);
  --bg-button: rgba(35, 35, 35, 0.3);
  --bg-button-primary: rgba(37, 99, 235, 0.85);
  --bg-button-hover: rgba(45, 45, 45, 0.4);
  --bg-row-hover: rgba(40, 40, 40, 0.25);
  --bg-selection: rgba(37, 99, 235, 0.25);

  --bg-icon-blue: rgba(147, 197, 253, 0.2);
  --bg-icon-green: rgba(216, 180, 254, 0.2);
  --bg-icon-orange: rgba(253, 186, 116, 0.2);
  --bg-icon-red: rgba(252, 165, 165, 0.2);
  --bg-gender-male: rgba(147, 197, 253, 0.2);
  --bg-gender-female: rgba(216, 180, 254, 0.2);

  --text-primary: #f4f4f5;
  --text-secondary: #e4e4e7;
  --text-tertiary: rgba(161, 161, 170, 0.85);
  --text-button: #f4f4f5;
  --text-button-primary: #ffffff;
  --text-positive: #4ade80;
  --text-negative: #f87171;
  --text-selection: #93c5fd;

  --text-icon-blue: #93c5fd;
  --text-icon-green: #d8b4fe;
  --text-icon-orange: #fdba74;
  --text-icon-red: #fca5a5;
  --text-gender-male: #93c5fd;
  --text-gender-female: #d8b4fe;

  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.14);
  --border-active: rgba(37, 99, 235, 0.45);
  --border-focus: rgba(147, 197, 253, 0.7);

  --shadow-primary: 0 8px 32px rgba(0, 0, 0, 0.24);
  --shadow-secondary: 0 12px 40px rgba(0, 0, 0, 0.36);
  --shadow-button-primary: 0 4px 16px rgba(37, 99, 235, 0.35);
  --shadow-icon: 0 4px 16px rgba(0, 0, 0, 0.3);

  --status-active: #4ade80;
  --status-inactive: #f87171;
}

/* ===== CSS RESET & BASE ===== */
*,
::before,
::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
}

::selection {
  background: var(--bg-selection);
  color: var(--text-selection);
}

/* ===== TYPOGRAPHY ===== */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  background: var(--bg-primary);
  color: var(--text-secondary);
  min-height: 100vh;
  backdrop-filter: var(--blur-heavy);
  transition: background var(--transition-slow), color var(--transition-slow);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  scrollbar-width: thin;
  scrollbar-color: rgba(128, 128, 128, 0.5) transparent;
  overflow: hidden;
}

/* ===== WEBKIT SCROLLBARS ===== */
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(128, 128, 128, 0.3);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(128, 128, 128, 0.5);
  background-clip: content-box;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
}

h1 {
  font-size: 24px;
  margin-bottom: 4px;
}

h2 {
  font-size: 18px;
}

h3 {
  font-size: 16px;
}

p {
  color: var(--text-tertiary);
  font-size: 14px;
}

.sidebar-title {
  font-size: 11px;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-block: 8px;
  padding: 0 16px;
  font-weight: 600;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.epic-code {
  font-family: "SF Mono", "Monaco", "Consolas", monospace;
  color: var(--text-tertiary);
  font-size: 12px;
  background: var(--bg-input);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  backdrop-filter: var(--blur-light);
}

/* ===== LAYOUT COMPONENTS ===== */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 100vh;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  backdrop-filter: var(--blur-medium);
  -webkit-backdrop-filter: var(--blur-medium);
  z-index: 10;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateZ(0);
  will-change: transform;
  contain: layout style paint;
}

.sidebar-header {
  padding: 36px 0 0;
  flex-shrink: 0;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 0 8px;
  height: calc(100vh - 120px);
  scrollbar-gutter: stable;
}

.sidebar-footer {
  flex-shrink: 0;
  border-top: 1px solid var(--border-primary);
  margin-top: auto;
}

.sidebar-section {
  margin-block: 8px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: transform var(--transition-fast), background-color var(--transition-fast), color var(--transition-fast);
  border-left: 3px solid transparent;
  margin: 0 8px;
  border-radius: var(--radius-sm);
  text-decoration: none;
}

.sidebar-item:hover {
  background: var(--bg-row-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.sidebar-item.is-active {
  background: rgba(96, 165, 250, 0.15);
  color: var(--text-icon-blue);
  border-left-color: var(--text-icon-blue);
}

.sidebar-item .icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-content {
  margin-left: 250px;
  padding: var(--space-md) 10px var(--space-md) var(--space-2xl);
  min-height: calc(100vh - 32px);
  height: 100vh;
  position: relative;
  overflow-y: auto;
  scrollbar-gutter: stable;
  transform: translateZ(0);
  will-change: scroll-position;
  contain: layout style;
}

.footer-summary {
  text-align: center;
  padding: 8px 16px;
  border-top: 1px solid var(--border-primary);
  backdrop-filter: var(--blur-medium);
}

.footer-summary small {
  font-size: 11px;
  color: var(--text-tertiary);
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* ===== TOOLBAR ===== */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  backdrop-filter: var(--blur-medium);
  border: 1px solid var(--border-primary);
  -webkit-app-region: var(--app-region-drag);
}

.toolbar-left h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.toolbar-left p {
  color: var(--text-tertiary);
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  -webkit-app-region: var(--app-region-no-drag);
}

/* ===== BUTTONS ===== */
.btn {
  padding: 8px 16px;
  border: 1px solid var(--border-secondary);
  background: var(--bg-button);
  color: var(--text-button);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 13px;
  transition: background-color var(--transition-fast), border-color var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: var(--blur-light);
  outline: none;
  -webkit-app-region: var(--app-region-no-drag);
  text-decoration: none;
}

.btn:hover {
  background: var(--bg-button-hover);
  border-color: var(--border-secondary);
}

.btn:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn-primary {
  background: var(--bg-button-primary);
  border-color: rgba(37, 99, 235, 0.3);
  color: var(--text-button-primary);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  border-color: rgba(29, 78, 216, 0.3);
}

.filter-btn.is-active {
  background: rgba(96, 165, 250, 0.1);
  border-color: var(--border-active);
  color: var(--text-icon-blue);
}

/* ===== STATS CARDS ===== */
.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 20px;
  position: relative;
  backdrop-filter: var(--blur-medium);
  transition: transform var(--transition-medium), box-shadow var(--transition-medium);
  cursor: default;
  transform: translateZ(0);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary);
}

.stat-card .stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.stat-card .stat-label {
  color: var(--text-tertiary);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-card .stat-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  backdrop-filter: var(--blur-light);
}

.stat-icon svg {
  width: 16px;
  height: 16px;
}

.stat-card .stat-icon.blue {
  background: var(--bg-icon-blue);
  color: var(--text-icon-blue);
}

.stat-card .stat-icon.green {
  background: var(--bg-icon-green);
  color: var(--text-icon-green);
}

.stat-card .stat-icon.orange {
  background: var(--bg-icon-orange);
  color: var(--text-icon-orange);
}

.stat-card .stat-icon.red {
  background: var(--bg-icon-red);
  color: var(--text-icon-red);
}

.stat-card .stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-card .stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-card .stat-change.positive {
  color: var(--text-positive);
}

.stat-card .stat-change.negative {
  color: var(--text-negative);
}

/* ===== FILTER PANEL ===== */
.filter-options-container {
  display: grid;
  grid-template-rows: 0fr;
  overflow: hidden;
  transition: grid-template-rows var(--duration) var(--ease-out);
}

.filter-options-container.is-open {
  grid-template-rows: 1fr;
  margin-bottom: 24px;
}

.filter-options {
  position: relative;
  z-index: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.filter-options-container.is-open .filter-options {
  border: 1px solid var(--border-primary);
}

.filter-options > * {
  position: relative;
  z-index: 2;
}

.filter-options::before {
  content: '';
  position: absolute;
  z-index: 0;
  top: 50%;
  left: 50%;
  width: 200%;
  aspect-ratio: 1;
  transform: translate(-50%, -50%);
  background: conic-gradient(
    from 0deg,
    transparent 0%,
    var(--border-active, white) 10%,
    transparent 35%
  );
  opacity: 0;
}

.filter-options::after {
  content: '';
  position: absolute;
  z-index: 1;
  background: var(--bg-primary);
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  border-radius: calc(var(--radius-lg) - 1px);
}

.filter-options-container.animate-sweep .filter-options::before {
  opacity: 1;
  animation: light-sweep 1.5s linear;
}

@keyframes light-sweep {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.filter-header {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: 14px 18px;
  margin: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: var(--blur-light);
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-filters {
  color: var(--text-icon-blue);
  background: none;
  border: none;
  font-size: 13px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.clear-filters:hover {
  background: rgba(37, 99, 235, 0.1);
}

.filter-controls {
  display: flex;
  align-items: flex-end;
  padding: 24px 20px;
  gap: 20px;
  overflow-x: auto;
  flex-wrap: wrap;
}

.filter-section {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  flex-shrink: 0;
}

.filter-section:last-child {
  flex-grow: 1;
  padding-right: 5%;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.filter-group label {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
}

.filter-group input,
.filter-group select {
  background: var(--bg-input);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: 8px 12px;
  color: var(--text-secondary);
  font-size: 13px;
  backdrop-filter: var(--blur-light);
  transition: border-color var(--transition-medium), box-shadow var(--transition-medium);
  -webkit-app-region: var(--app-region-no-drag);
  width: 100%;
  min-width: 120px;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: var(--border-active);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.select-wrapper {
  position: relative;
}

.select-wrapper::after {
  content: "";
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--text-tertiary);
  pointer-events: none;
}

.select-wrapper select {
  -webkit-appearance: none;
  appearance: none;
  padding-right: 30px;
}

/* ===== DATA TABLE ===== */
.data-table {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  backdrop-filter: var(--blur-medium);
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}

.table-header {
  position: relative;
  z-index: 50;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: var(--blur-light);
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-container {
  position: relative;
}

.search-input {
  background: var(--bg-input);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: 8px 12px 8px 32px;
  color: var(--text-secondary);
  font-size: 13px;
  backdrop-filter: var(--blur-light);
  transition: border-color var(--transition-medium), box-shadow var(--transition-medium);
  -webkit-app-region: var(--app-region-no-drag);
  width: 240px;
}

.search-input:focus {
  outline: none;
  border-color: var(--border-active);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  opacity: 0.4;
  pointer-events: none;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  transform: translateZ(0);
}

th {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  font-weight: 500;
  font-size: 12px;
  text-align: left;
  padding: 12px 0 12px 20px;
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: var(--blur-light);
  user-select: none;
  position: relative;
}

td {
  padding: 8px 0 8px 20px;
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-secondary);
  font-size: 13px;
  cursor: default;
  position: relative;
}

th:last-child,
td:last-child {
  padding: 12px 20px;
}

tr {
  transition: background-color var(--transition-fast);
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background: var(--bg-row-hover);
}

tr.selected {
  background: var(--bg-selection);
}

.name-cell {
  font-weight: 500;
  color: var(--text-primary);
}

.gender-badge {
  padding: 3px 6px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  backdrop-filter: var(--blur-light);
}

.gender-badge.male {
  background: var(--bg-gender-male);
  color: var(--text-gender-male);
}

.gender-badge.female {
  background: var(--bg-gender-female);
  color: var(--text-gender-female);
}

.action-menu {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 14px;
  transition: background-color var(--transition-fast), color var(--transition-fast);
}

.action-menu:hover {
  background: var(--bg-row-hover);
  color: var(--text-primary);
}

.table-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-primary);
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: var(--blur-light);
}

.table-info {
  color: var(--text-tertiary);
  font-size: 12px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-btn {
  padding: 6px 12px;
  background: var(--bg-input);
  border: 1px solid var(--border-secondary);
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 12px;
  transition: background-color var(--transition-fast), color var(--transition-fast);
  backdrop-filter: var(--blur-light);
}

.page-btn svg {
  position: relative;
  margin-bottom: -2px;
}

.page-btn:hover {
  background: var(--bg-button-hover);
  color: var(--text-primary);
}

.page-btn.is-active {
  background: var(--bg-button-primary);
  border-color: rgba(37, 99, 235, 0.3);
  color: white;
}

/* ===== STATUS INDICATORS ===== */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 0 8px currentColor;
}

.status-indicator.status-active {
  background: var(--status-active);
}

.status-indicator.status-inactive,
.status-indicator.status-expired {
  background: var(--status-inactive);
}

/* ===== EMPTY STATE ===== */
.empty-state {
  text-align: center;
  padding: 60px 20px !important;
  background: var(--bg-secondary);
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 300px;
  margin: 0 auto;
}

.empty-state-content svg {
  color: var(--text-tertiary);
  opacity: 0.6;
}

.empty-state-content h3 {
  margin: 0;
  color: var(--text-secondary);
  font-size: 18px;
  font-weight: 600;
}

.empty-state-content p {
  margin: 0;
  color: var(--text-tertiary);
  font-size: 14px;
  line-height: 1.5;
}

/* ===== TREE VIEW ===== */
.tree {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tree ul {
  list-style: none;
  padding-left: 14px;
  border-left: 1px solid var(--border-primary);
  margin-left: 7px;
}

.tree details {
  padding-left: 16px;
}

.tree summary {
  list-style: none;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
  color: var(--text-secondary);
  outline: none;
}

.tree summary:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.tree summary::-webkit-details-marker {
  display: none;
}

.tree summary svg {
  color: var(--text-tertiary);
  transition: transform var(--transition-medium);
  transform-origin: center;
  width: 14px;
  height: 14px;
  margin-inline: 10px;
  flex-shrink: 0;
}

.tree details[open] > summary {
  color: var(--text-primary);
}

.tree details[open] > summary svg {
  transform: rotate(90deg);
}

.tree details > summary > .checkbox-container {
  width: 100%;
  height: 32px;
}

.tree details ul {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

summary > .checkbox-container {
  font-weight: 500;
}

details ul .checkbox-container {
  width: 162px;
  height: 24px;
}

/* ===== CHECKBOXES ===== */
.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color var(--transition-fast);
  position: relative;
  font-size: 13px;
}

.checkbox-container input[type=checkbox] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-container .checkbox-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 158px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-container.child .checkbox-label {
  width: 136px;
}

.voter-count {
  font-size: 11px;
  color: var(--text-tertiary);
  font-weight: 500;
  margin-left: 4px;
  flex-shrink: 0;
}

/* ===== IMPORT PROGRESS ===== */
.import-progress {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.import-progress-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.spinner {
  animation: spin 1s linear infinite;
  color: var(--primary-color);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.checkbox-container .checkmark {
  position: relative;
  height: 16px;
  width: 16px;
  background: var(--bg-input);
  border: 1px solid var(--border-secondary);
  border-radius: 5px;
  margin-right: 10px;
  transition: background-color var(--transition-fast), border-color var(--transition-fast), box-shadow var(--transition-fast);
  backdrop-filter: var(--blur-light);
  flex-shrink: 0;
}

.checkbox-container .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid var(--text-button-primary);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-container input[type=checkbox]:checked {
  color: var(--text-primary);
}

.checkbox-container input[type=checkbox]:checked ~ .checkmark {
  background: var(--bg-button-primary);
  border-color: rgba(37, 99, 235, 0.3);
  box-shadow: var(--shadow-button-primary);
}

.checkbox-container input[type=checkbox]:checked ~ .checkmark:after {
  display: block;
}

/* ===== VOTER DETAIL PANEL ===== */
.voter-detail-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 464px;
  height: 100%;
  background: var(--bg-overlay);
  backdrop-filter: var(--blur-heavy);
  border-left: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform var(--duration) var(--ease-out);
  z-index: 100;
  will-change: transform;
}

.voter-detail-panel.is-open {
  transform: translateX(0);
  box-shadow: var(--shadow-primary);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary);
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.panel-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.voter-detail-panel .btn-edit,
.voter-detail-panel .btn-save {
  font-size: 12px;
  padding: 6px 12px;
}

.panel-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast), color var(--transition-fast);
}

.panel-close:hover {
  background: var(--bg-row-hover);
  color: var(--text-primary);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 4px 16px 16px;
  display: flex;
  flex-direction: column;
  scrollbar-gutter: stable;
}

.voter-detail-panel .panel-section {
  margin-bottom: 12px;
}

.voter-detail-panel .panel-section details {
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--border-primary);
}

.voter-detail-panel .panel-section details summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  padding: 16px;
  transition: color var(--duration) var(--ease-out);
  border-bottom: 1px solid transparent;
  list-style: none;
  color: var(--text-secondary);
  outline: none;
  background: var(--bg-secondary);
}

.voter-detail-panel .panel-section details summary::-webkit-details-marker {
  display: none;
}

.voter-detail-panel .panel-section details summary .summary-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.voter-detail-panel .panel-section details summary svg {
  color: var(--text-tertiary);
  transition: transform var(--transition-medium);
  transform-origin: center;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.voter-detail-panel .panel-section details[open] > summary {
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-primary);
}

.voter-detail-panel .panel-section details[open] > summary svg {
  transform: rotate(90deg);
}

.voter-detail-panel .panel-subsection {
  padding: 16px;
  border-bottom: 1px solid var(--border-primary);
}

.voter-detail-panel .panel-subsection:last-child {
  border-bottom: none;
}

.voter-detail-panel .panel-subsection h4 {
  margin-bottom: 16px;
  color: var(--text-primary);
  font-weight: 600;
}

.voter-detail-panel .panel-subsection .section-grid {
  padding-inline: 0;
}

.voter-detail-panel .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.voter-detail-panel .section-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 16px;
}

.voter-detail-panel .detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.voter-detail-panel .detail-label {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
}

.voter-detail-panel .detail-value {
  font-size: 13px;
  color: var(--text-secondary);
  background: var(--bg-input);
  padding: 8px 12px;
  border-radius: var(--radius-md);
  backdrop-filter: var(--blur-light);
  border: 1px solid var(--border-primary);
  width: 100%;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.voter-detail-panel .detail-value.editable {
  cursor: text;
  transition: border-color var(--transition-fast);
}

.voter-detail-panel .detail-value.editable:hover {
  border-color: var(--border-secondary);
}

.voter-detail-panel .detail-value.editable:focus {
  border-color: var(--border-focus);
  outline: none;
}

.voter-detail-panel .family-members {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
}

.voter-detail-panel .family-member {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--bg-input);
  border-radius: var(--radius-sm);
  backdrop-filter: var(--blur-light);
}

.voter-detail-panel .member-name {
  color: var(--text-primary);
  flex: 1;
}

.voter-detail-panel .member-relation {
  font-size: 11px;
  color: var(--text-tertiary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.voter-detail-panel .status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.voter-detail-panel .status-item {
  grid-column: 1/-1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
}

.voter-detail-panel .status-label {
  flex-grow: 1;
}

.voter-detail-panel .status-value {
  font-size: 12px;
  color: var(--text-tertiary);
}

.transactions-table table {
  width: 100%;
  border-collapse: collapse;
}

/* .transactions-table th,
.transactions-table td {
  padding: 8px 12px;
  text-align: left;
} */

.transactions-table th {
  font-weight: 600;
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.transactions-table thead tr {
  border-bottom: 2px solid var(--border-primary);
}

.transactions-table td {
  color: var(--text-tertiary);
  border-top: 1px solid var(--border-primary);
}

.transactions-table tfoot td {
  font-weight: 600;
  color: var(--text-primary);
}

/* ===== DROPDOWN & CONTEXT MENUS ===== */
.dropdown-container {
  position: relative;
  display: block;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: calc(100% + 8px);
  background: var(--bg-overlay);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  backdrop-filter: var(--blur-light);
  box-shadow: var(--shadow-primary);
  min-width: 164px;
  overflow: hidden;
  padding: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity var(--transition-medium), transform var(--transition-medium), visibility var(--transition-medium);
  pointer-events: none;
  z-index: 1000;
}

.dropdown-menu.is-open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  color: var(--text-secondary);
  font-size: 13px;
}

.dropdown-item:hover {
  background: var(--bg-row-hover);
  color: var(--text-primary);
}

.dropdown-item.is-selected {
  color: var(--text-icon-blue);
}

.dropdown-item-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.dropdown-menu-up {
  top: auto;
  right: auto;
  left: 24px;
  bottom: calc(100% + 8px);
  transform: translateY(10px);
}

.dropdown-menu-up.is-open {
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
  color: var(--text-secondary);
  transition: background-color var(--transition-fast), color var(--transition-fast);
  border-radius: var(--radius-sm);
  gap: 10px;
}

.dropdown-item:hover {
  background: var(--bg-row-hover);
  color: var(--text-primary);
}

.dropdown-item .dropdown-item-icon {
  width: 16px;
  height: 16px;
  color: var(--text-icon-blue);
  opacity: 1;
  transition: opacity var(--transition-fast);
}

.dropdown-item.is-selected .dropdown-item-icon {
  opacity: 1;
}

/* Show checkmark icons in columns dropdown when selected */
#columns-dropdown .dropdown-item.is-selected .dropdown-item-icon {
  opacity: 1;
}

/* Hide checkmark icons in columns dropdown when not selected */
#columns-dropdown .dropdown-item:not(.is-selected) .dropdown-item-icon {
  opacity: 0;
}

/* Keep icons visible in settings dropdown */
#settings-dropdown .dropdown-item .dropdown-item-icon {
  opacity: 1;
}

.context-menu {
  position: absolute;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  backdrop-filter: var(--blur-heavy);
  box-shadow: var(--shadow-secondary);
  z-index: 50;
  min-width: 164px;
  overflow: hidden;
  display: none;
}

.context-menu.is-active {
  display: block;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 13px;
  color: var(--text-secondary);
  transition: background-color var(--transition-fast), color var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-menu-item:hover {
  background: var(--bg-row-hover);
  color: var(--text-primary);
}

.context-menu-divider {
  height: 1px;
  background: var(--border-primary);
  margin: 4px 0;
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
  background: var(--bg-secondary);
  color: var(--text-tertiary);
}

.badge.badge-primary {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.badge.badge-count {
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  margin-right: 8px;
}

.badge.badge-amount {
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  margin-right: 8px;
}

/* ===== SCROLLBARS ===== */
::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
  background: transparent;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: rgba(128, 128, 128, 0.4);
  border-radius: 6px;
  border: 2px solid var(--bg-secondary);
  background-clip: content-box;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(128, 128, 128, 0.6);
}

::-webkit-scrollbar-thumb:active {
  background: rgba(128, 128, 128, 0.8);
}

[data-theme=dark] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme=dark] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--bg-secondary);
}

[data-theme=dark] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

[data-theme=dark] ::-webkit-scrollbar-thumb:active {
  background: rgba(255, 255, 255, 0.4);
}

[data-theme=dark] * {
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

/* ===== UTILITY CLASSES ===== */
.draggable, [data-tauri-drag-region] {
  -webkit-app-region: var(--app-region-drag);
}

.non-draggable {
  -webkit-app-region: var(--app-region-no-drag);
}

[data-tauri-drag-region],
.btn, .sidebar-item, .stat-card, .checkbox-container,
th, .gender-badge, .action-menu, .dropdown-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.js-focus-visible .btn:focus:not(.focus-visible),
.js-focus-visible .sidebar-item:focus:not(.focus-visible) {
  outline: none;
}

/* ===== MEDIA QUERIES ===== */
@media (max-width: 1200px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .main-content {
    margin-left: 200px;
  }

  .toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
    z-index: 100;
  }

  .sidebar.visible {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    padding: 12px;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .filter-section {
    flex-direction: column;
    width: 100%;
  }

  .filter-group input,
  .filter-group select {
    width: 100%;
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .btn, .sidebar-item {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
}

@media screen and (min-resolution: 144dpi) {
  ::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }
}

/* ===== SETTINGS DASHBOARD ===== */
.settings-dashboard-container {
  display: grid;
  grid-template-rows: 0fr;
  overflow: hidden;
  transition: grid-template-rows var(--duration) var(--ease-out);
}

.settings-dashboard-container.is-open {
  grid-template-rows: 1fr;
  margin-bottom: 24px;
}

.settings-dashboard {
  position: relative;
  z-index: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.settings-dashboard-container.is-open .settings-dashboard {
  border: 1px solid var(--border-primary);
}

.settings-dashboard > * {
  position: relative;
  z-index: 2;
}

.settings-dashboard::before {
  content: '';
  position: absolute;
  z-index: 0;
  top: 50%;
  left: 50%;
  width: 200%;
  aspect-ratio: 1;
  transform: translate(-50%, -50%);
  background: conic-gradient(
    from 0deg,
    transparent 0%,
    var(--border-active, white) 10%,
    transparent 35%
  );
  opacity: 0;
}

.settings-dashboard::after {
  content: '';
  position: absolute;
  z-index: 1;
  background: var(--bg-primary);
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  border-radius: calc(var(--radius-lg) - 1px);
}

.settings-dashboard-container.animate-sweep .settings-dashboard::before {
  opacity: 1;
  animation: light-sweep 1.5s linear;
}

.settings-dashboard .filter-header {
  margin: 2px;
}

.settings-controls {
  display: flex;
  align-items: flex-end;
  padding: 16px;
  gap: 16px;
  overflow-x: auto;
}

.add-item-form {
  display: flex;
  gap: 8px;
  align-items: center;
}

#new-item-input {
  background: var(--bg-input);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: 8px 12px;
  color: var(--text-secondary);
  font-size: 13px;
  backdrop-filter: var(--blur-light);
  width: 200px;
}

#new-item-input:focus {
  outline: none;
  border-color: var(--border-active);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.data-list {
  padding: 16px;
}

.data-list h3 {
  font-size: 16px;
  margin-bottom: 12px;
}

.data-list ul {
  list-style: none;
  padding: 0;
}

.data-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--bg-input);
  border-radius: var(--radius-sm);
  margin-bottom: 8px;
}

.data-list .item-actions button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: var(--text-tertiary);
}

/* ===== BADGES ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
  backdrop-filter: var(--blur-light);
}

.badge-count {
  background: var(--bg-icon-blue);
  color: var(--text-icon-blue);
}

.badge-amount {
  background: var(--bg-icon-green);
  color: var(--text-icon-green);
}

/* ===== NOTES CONTENT ===== */
.notes-content {
  padding: 12px;
  background: var(--bg-input);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.note-text {
  color: var(--text-secondary);
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
}

/* ===== BUTTON IMPROVEMENTS ===== */
.btn-small {
  padding: 4px 8px;
  font-size: 11px;
}