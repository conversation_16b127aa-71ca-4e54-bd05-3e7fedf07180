{"name": "electixir", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "sql.js": "^1.13.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/sql.js": "^1.4.9", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}